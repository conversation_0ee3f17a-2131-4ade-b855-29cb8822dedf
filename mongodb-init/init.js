// MongoDB initialization script
// This script will be executed when MongoDB container starts

db = db.getSiblingDB('visiobox');

// Create collections with validation
db.createCollection('files', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['telegramFileId', 'originalFileName', 'fileSize', 'mimeType'],
      properties: {
        telegramFileId: {
          bsonType: 'string',
          description: 'Telegram file ID is required'
        },
        originalFileName: {
          bsonType: 'string',
          description: 'Original file name is required'
        },
        fileSize: {
          bsonType: 'number',
          minimum: 0,
          description: 'File size must be a positive number'
        },
        mimeType: {
          bsonType: 'string',
          description: 'MIME type is required'
        },
        uploadDate: {
          bsonType: 'date'
        },
        parentId: {
          bsonType: ['objectId', 'null']
        },
        isDeleted: {
          bsonType: 'bool'
        },
        deletedAt: {
          bsonType: ['date', 'null']
        },
        ownerId: {
          bsonType: ['objectId', 'null']
        },
        isProcessingAI: {
          bsonType: 'bool'
        },
        aiMetadata: {
          bsonType: 'object'
        }
      }
    }
  }
});

db.createCollection('folders', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['folderName'],
      properties: {
        folderName: {
          bsonType: 'string',
          description: 'Folder name is required'
        },
        parentId: {
          bsonType: ['objectId', 'null']
        },
        createdAt: {
          bsonType: 'date'
        },
        isDeleted: {
          bsonType: 'bool'
        },
        deletedAt: {
          bsonType: ['date', 'null']
        },
        ownerId: {
          bsonType: ['objectId', 'null']
        }
      }
    }
  }
});

// Create indexes for better performance
db.files.createIndex({ telegramFileId: 1 });
db.files.createIndex({ parentId: 1, isDeleted: 1 });
db.files.createIndex({ originalFileName: 1, isDeleted: 1 });
db.files.createIndex({ uploadDate: -1 });
db.files.createIndex({ ownerId: 1, isDeleted: 1 });

db.folders.createIndex({ parentId: 1, isDeleted: 1 });
db.folders.createIndex({ folderName: 1, isDeleted: 1 });
db.folders.createIndex({ createdAt: -1 });
db.folders.createIndex({ ownerId: 1, isDeleted: 1 });
db.folders.createIndex({ folderName: 1, parentId: 1, isDeleted: 1 }, { unique: true });

print('Database initialized successfully!');
