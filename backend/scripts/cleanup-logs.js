#!/usr/bin/env node

/**
 * CLI script for manual log cleanup operations
 * Usage:
 *   node scripts/cleanup-logs.js --help
 *   node scripts/cleanup-logs.js --stats
 *   node scripts/cleanup-logs.js --cleanup --category=security --days=365
 *   node scripts/cleanup-logs.js --cleanup --dry-run
 *   node scripts/cleanup-logs.js --archive
 *   node scripts/cleanup-logs.js --archive-info
 */

const logCleanupService = require('../lib/services/logCleanupService');

// Simple argument parser
function parseArgs() {
  const args = process.argv.slice(2);
  const options = {};

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];

    if (arg === '--help' || arg === '-h') {
      showHelp();
      process.exit(0);
    } else if (arg === '--stats' || arg === '-s') {
      options.stats = true;
    } else if (arg === '--cleanup' || arg === '-c') {
      options.cleanup = true;
    } else if (arg === '--archive' || arg === '-a') {
      options.archive = true;
    } else if (arg === '--archive-info') {
      options.archiveInfo = true;
    } else if (arg === '--cleanup-archives') {
      options.cleanupArchives = true;
    } else if (arg === '--dry-run') {
      options.dryRun = true;
    } else if (arg.startsWith('--category=')) {
      options.category = arg.split('=')[1];
    } else if (arg.startsWith('--days=')) {
      options.days = parseInt(arg.split('=')[1]);
    } else if (arg.startsWith('--archive-retention=')) {
      options.archiveRetention = parseInt(arg.split('=')[1]);
    }
  }

  return options;
}

function showHelp() {
  console.log(`
VisioBox Log Cleanup Tool

Usage:
  node scripts/cleanup-logs.js [options]

Options:
  --help, -h              Show this help message
  --stats, -s             Show cleanup statistics
  --cleanup, -c           Perform log cleanup
  --category=<category>   Cleanup specific category (security, authentication, fileOperations, userBehavior, system)
  --days=<days>           Cleanup logs older than specified days
  --dry-run               Show what would be deleted without actually deleting
  --archive, -a           Perform log archiving
  --archive-info          Show archive information
  --cleanup-archives      Cleanup old archive files
  --archive-retention=<days>  Archive retention period in days (default: 365)

Examples:
  node scripts/cleanup-logs.js --stats
  node scripts/cleanup-logs.js --cleanup --dry-run
  node scripts/cleanup-logs.js --cleanup --category=security --days=365
  node scripts/cleanup-logs.js --archive
  node scripts/cleanup-logs.js --archive-info
  node scripts/cleanup-logs.js --cleanup-archives --archive-retention=180
`);
}

const options = parseArgs();

async function main() {
  try {
    console.log('📋 VisioBox Log Cleanup Tool\n');

    if (options.stats) {
      console.log('📊 Getting cleanup statistics...\n');
      const stats = await logCleanupService.getCleanupStats();

      console.log('Cleanup Statistics:');
      console.log('==================');

      for (const [category, data] of Object.entries(stats)) {
        console.log(`\n${category.toUpperCase()}:`);
        console.log(`  Total logs: ${data.total}`);
        console.log(`  Eligible for cleanup: ${data.eligibleForCleanup}`);
        console.log(`  Retention period: ${data.retentionDays} days`);
        console.log(`  Cutoff date: ${data.cutoffDate}`);
      }

    } else if (options.cleanup) {
      console.log('🧹 Performing log cleanup...\n');

      const cleanupOptions = {
        category: options.category,
        olderThanDays: options.days,
        dryRun: options.dryRun || false
      };

      if (options.dryRun) {
        console.log('🔍 DRY RUN MODE - No logs will be deleted\n');
      }

      if (options.category) {
        console.log(`📂 Category filter: ${options.category}`);
      }

      if (options.days) {
        console.log(`📅 Age filter: older than ${options.days} days`);
      }

      console.log('');

      const result = await logCleanupService.performManualCleanup(cleanupOptions);

      if (result.success) {
        if (options.dryRun) {
          console.log(`✅ Dry run completed: ${result.wouldDelete} logs would be deleted`);
        } else {
          console.log(`✅ Cleanup completed: ${result.deletedCount} logs deleted`);
        }
      } else {
        console.error(`❌ Cleanup failed: ${result.error}`);
        process.exit(1);
      }

    } else if (options.archive) {
      console.log('📦 Performing log archiving...\n');

      const result = await logCleanupService.performArchiving();

      if (result.success) {
        console.log(`✅ Archiving completed: ${result.archivedCount} logs archived in ${result.duration}ms`);
      } else {
        console.error(`❌ Archiving failed: ${result.error}`);
        process.exit(1);
      }

    } else if (options.archiveInfo) {
      console.log('📋 Getting archive information...\n');

      const info = await logCleanupService.getArchiveInfo();

      console.log('Archive Information:');
      console.log('===================');
      console.log(`Archive path: ${info.archivePath}`);
      console.log(`Total files: ${info.totalFiles}`);

      if (info.files.length > 0) {
        console.log('\nArchive files:');
        info.files.forEach(file => {
          const sizeKB = Math.round(file.size / 1024);
          console.log(`  ${file.filename} (${sizeKB} KB) - Created: ${file.created.toISOString()}`);
        });
      } else {
        console.log('\nNo archive files found.');
      }

    } else if (options.cleanupArchives) {
      const retentionDays = options.archiveRetention || 365;
      console.log(`🗑️  Cleaning up archive files older than ${retentionDays} days...\n`);

      const result = await logCleanupService.cleanupArchives(retentionDays);

      if (result.success) {
        console.log(`✅ Archive cleanup completed: ${result.deletedCount} files deleted`);
      } else {
        console.error(`❌ Archive cleanup failed: ${result.error}`);
        process.exit(1);
      }

    } else {
      console.log('❓ No action specified. Use --help for usage information.');
      showHelp();
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

// Run the script
main();
