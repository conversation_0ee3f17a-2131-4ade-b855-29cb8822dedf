#!/usr/bin/env node

/**
 * Configuration Validation Script
 * Checks if all required environment variables are properly set
 */

const path = require('path');
const fs = require('fs');

// Load environment variables
const envPath = path.join(__dirname, '../../.env');
if (fs.existsSync(envPath)) {
  require('dotenv').config({ path: envPath });
} else {
  console.error('❌ .env file not found. Please copy .env.example to .env and configure it.');
  process.exit(1);
}

// Import config
const config = require('../lib/config');

/**
 * Validation rules
 */
const validationRules = {
  required: [
    'SECRET_KEY',
    'SESSION_SECRET',
    'TELEGRAM_BOT_TOKEN',
    'TELEGRAM_CHAT_ID'
  ],
  optional: [
    'TELEGRAM_ADMIN_CHAT_ID',
    'EMAIL_USER_1',
    'EMAIL_PASS_1',
    'EMAIL_ALERTS'
  ]
};

/**
 * Validate configuration
 */
function validateConfig() {
  console.log('🔍 Validating VisiBox configuration...\n');

  let hasErrors = false;
  let hasWarnings = false;

  // Check required variables
  console.log('📋 Required Configuration:');
  validationRules.required.forEach(key => {
    const value = process.env[key];
    if (!value || value.trim() === '') {
      console.log(`❌ ${key}: Missing or empty`);
      hasErrors = true;
    } else {
      console.log(`✅ ${key}: Configured`);
    }
  });

  console.log('\n📋 Optional Configuration:');
  validationRules.optional.forEach(key => {
    const value = process.env[key];
    if (!value || value.trim() === '') {
      console.log(`⚠️  ${key}: Not configured (optional)`);
      hasWarnings = true;
    } else {
      console.log(`✅ ${key}: Configured`);
    }
  });

  // Check email configuration
  console.log('\n📧 Email Configuration:');
  const emailConfigs = config.emailInfos;
  if (emailConfigs.length === 0) {
    console.log('⚠️  No email accounts configured');
    hasWarnings = true;
  } else {
    console.log(`✅ ${emailConfigs.length} email account(s) configured`);
    emailConfigs.forEach((email, index) => {
      console.log(`   📧 Account ${index + 1}: ${email.auth.user}`);
    });
  }

  // Check email alerts
  const emailAlerts = config.listEmailAlert;
  if (emailAlerts.length === 0) {
    console.log('⚠️  No email alerts configured');
    hasWarnings = true;
  } else {
    console.log(`✅ ${emailAlerts.length} email alert(s) configured`);
    emailAlerts.forEach((email, index) => {
      console.log(`   📧 Alert ${index + 1}: ${email}`);
    });
  }

  // Check database configuration
  console.log('\n🗄️  Database Configuration:');
  console.log(`✅ MongoDB: ${config.mongo.connections.master.host}:${config.mongo.connections.master.port}/${config.mongo.connections.master.database}`);
  console.log(`✅ Redis: ${config.redis.connections.master.host}:${config.redis.connections.master.port}/${config.redis.connections.master.database}`);

  // Summary
  console.log('\n📊 Validation Summary:');
  if (hasErrors) {
    console.log('❌ Configuration has errors. Please fix the missing required variables.');
    console.log('💡 Check CONFIG_SETUP.md for detailed setup instructions.');
    process.exit(1);
  } else if (hasWarnings) {
    console.log('⚠️  Configuration is valid but has warnings.');
    console.log('✅ Application should start successfully.');
    console.log('💡 Consider configuring optional features for full functionality.');
  } else {
    console.log('✅ Configuration is complete and valid!');
    console.log('🚀 Ready to start the application.');
  }
}

/**
 * Show configuration tips
 */
function showTips() {
  console.log('\n💡 Configuration Tips:');
  console.log('• Use strong, unique values for SECRET_KEY and SESSION_SECRET');
  console.log('• Get Telegram bot token from @BotFather');
  console.log('• Use Gmail App Passwords for email configuration');
  console.log('• Test with one email account first, then add more as needed');
  console.log('• Email alerts support comma-separated multiple addresses');
  console.log('\n📖 For detailed setup instructions, see CONFIG_SETUP.md');
}

// Run validation
try {
  validateConfig();
  showTips();
} catch (error) {
  console.error('❌ Configuration validation failed:', error.message);
  console.log('\n💡 Make sure your .env file is properly formatted and all required variables are set.');
  process.exit(1);
}
