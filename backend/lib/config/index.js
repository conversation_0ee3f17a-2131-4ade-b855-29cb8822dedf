const fs = require('fs');
const path = require('path');

/**
 * Custom config loader that replaces the 'config' package
 * This module loads configuration from environment variables with fallbacks
 */

// Load .env file if it exists (for development)
const envPath = path.join(__dirname, '../../../.env');
if (fs.existsSync(envPath)) {
  require('dotenv').config({ path: envPath });
}

/**
 * Get environment variable with fallback value
 * @param {string} key - Environment variable key
 * @param {any} fallback - Fallback value if env var is not set
 * @returns {any} - Environment variable value or fallback
 */
function getEnvVar(key, fallback = null) {
  const value = process.env[key];

  // Return fallback if value is undefined, null, or empty string
  if (value === undefined || value === null || value === '') {
    return fallback;
  }

  // Convert string boolean values
  if (value === 'true') return true;
  if (value === 'false') return false;

  // Convert string numbers
  if (!isNaN(value) && !isNaN(parseFloat(value))) {
    return parseFloat(value);
  }

  return value;
}

/**
 * Parse array from environment variable
 * @param {string} envKey - Environment variable key
 * @param {string} separator - Separator for splitting (default: ',')
 * @param {any} fallback - Fallback value if env var is not set
 * @returns {array} - Parsed array or fallback
 */
function getEnvArray(envKey, separator = ',', fallback = []) {
  const value = process.env[envKey];
  if (!value) return fallback;

  return value.split(separator).map(item => item.trim()).filter(item => item.length > 0);
}

/**
 * Build email configurations from environment variables
 * Supports dynamic number of email accounts
 * @returns {array} - Array of email configurations
 */
function buildEmailConfigs() {
  const emailConfigs = [];
  let index = 1;

  // Keep checking for EMAIL_USER_X until no more found
  while (process.env[`EMAIL_USER_${index}`]) {
    const config = {
      service: getEnvVar(`EMAIL_SERVICE_${index}`, 'gmail'),
      auth: {
        user: getEnvVar(`EMAIL_USER_${index}`),
        pass: getEnvVar(`EMAIL_PASS_${index}`)
      }
    };

    // Only add if both user and pass are provided
    if (config.auth.user && config.auth.pass) {
      emailConfigs.push(config);
    }

    index++;
  }

  return emailConfigs;
}

/**
 * Configuration object with environment variables and fallbacks
 */
const config = {
  // Database Configuration
  redis: {
    connections: {
      master: {
        host: getEnvVar('REDIS_HOST', 'localhost'),
        port: getEnvVar('REDIS_PORT', 6379),
        database: getEnvVar('REDIS_DATABASE', 0),
        password: getEnvVar('REDIS_PASSWORD', null)
      }
    }
  },

  mongo: {
    connections: {
      master: {
        host: getEnvVar('MONGO_HOST', 'localhost'),
        port: getEnvVar('MONGO_PORT', 27017),
        database: getEnvVar('MONGO_DATABASE', 'visiobox'),
        options: {
          useUnifiedTopology: true,
          useNewUrlParser: true,
          user: getEnvVar('MONGO_USER', null),
          pass: getEnvVar('MONGO_PASSWORD', null)
        }
      }
    }
  },

  // Application Configuration
  port: getEnvVar('PORT', 3001),
  logLevel: getEnvVar('LOG_LEVEL', 'info'),
  secretKey: getEnvVar('SECRET_KEY', null), // No fallback for security
  serviceName: getEnvVar('SERVICE_NAME', 'VISIOBOX-BACKEND'),
  reactAppApiUrl: getEnvVar('REACT_APP_API_URL', 'http://localhost:3001'),

  // Telegram Configuration
  telegram: {
    botToken: getEnvVar('TELEGRAM_BOT_TOKEN', null), // No fallback for security
    chatId: getEnvVar('TELEGRAM_CHAT_ID', null), // No fallback for security
    adminChatId: getEnvVar('TELEGRAM_ADMIN_CHAT_ID', null) // No fallback for security
  },

  // OAuth Configuration
  oauth: {
    enabled: getEnvVar('OAUTH_ENABLED', false),
    google: {
      enabled: getEnvVar('OAUTH_GOOGLE_ENABLED', false),
      clientId: getEnvVar('OAUTH_GOOGLE_CLIENT_ID', null),
      clientSecret: getEnvVar('OAUTH_GOOGLE_CLIENT_SECRET', null),
      callbackURL: getEnvVar('OAUTH_GOOGLE_CALLBACK_URL', 'http://localhost:3001/auth/google/callback')
    },
    telegram: {
      enabled: getEnvVar('OAUTH_TELEGRAM_ENABLED', false),
      botToken: getEnvVar('OAUTH_TELEGRAM_BOT_TOKEN', null), // No fallback for security
      callbackURL: getEnvVar('OAUTH_TELEGRAM_CALLBACK_URL', 'http://localhost:3001/auth/telegram/callback')
    }
  },

  // Session Configuration
  session: {
    secret: getEnvVar('SESSION_SECRET', null), // No fallback for security
    maxAge: getEnvVar('SESSION_MAX_AGE', 86400000)
  },

  // Email Configuration - Dynamic array based on environment variables
  emailInfos: buildEmailConfigs(),

  // Email Alert Configuration - Dynamic array from comma-separated env var
  listEmailAlert: getEnvArray('EMAIL_ALERTS', ',', [])
};

/**
 * Get configuration value using dot notation
 * @param {string} path - Configuration path (e.g., 'mongo.connections.master.host')
 * @returns {any} - Configuration value
 */
function get(path) {
  return path.split('.').reduce((obj, key) => {
    return obj && obj[key] !== undefined ? obj[key] : null;
  }, config);
}

/**
 * Check if configuration has a specific path
 * @param {string} path - Configuration path
 * @returns {boolean} - True if path exists
 */
function has(path) {
  return get(path) !== null;
}

// Export config object with get and has methods to mimic the 'config' package
module.exports = {
  get,
  has,
  ...config
};
