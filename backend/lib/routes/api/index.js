const auth = require('./auth');
const user = require('./user');
const files = require('./files');
const browse = require('./browse');
const folders = require('./folders');
const items = require('./items');
const search = require('./search');
const trash = require('./trash');
const recentAccess = require('./recent-access');

module.exports = {
  auth,
  user,
  files,
  browse,
  folders,
  items,
  search,
  trash,
  recentAccess
};
