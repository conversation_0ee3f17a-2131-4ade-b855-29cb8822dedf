const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const telegramService = require('../../../../services/telegram');
const cacheService = require('../../../../services/cache');
const activityLogger = require('../../../../services/activityLogger');
const UserActivityLogModel = require('../../../../models/userActivityLog');
const RecentAccessModel = require('../../../../models/recentAccess');

module.exports = async (req, res) => {
  const startTime = Date.now();
  let file = null;

  try {
    const fileId = req.params.fileId;
    console.log(`📥 DOWNLOAD REQUEST: File ID: ${fileId}`);
    console.log(`📥 DOWNLOAD REQUEST: User: ${req.user ? req.user.id : 'No user'}`);

    if (!fileId) {
      return res.status(400).json({
        code: CONSTANTS.CODE.INVALID_PARAMS,
        message: 'File ID is required'
      });
    }

    const FileModel = require('../../../../models/file');
    const userId = req.user ? req.user.id : null;

    if (!userId) {
      return res.status(401).json({
        code: CONSTANTS.CODE.UNAUTHORIZED,
        message: 'Authentication required'
      });
    }

    file = await FileModel.findOne({
      _id: fileId,
      isDeleted: false,
      ownerId: userId // Only allow user's own files
    }).lean();

    console.log(`📥 DOWNLOAD: File found: ${file ? 'Yes' : 'No'}`);
    if (file) {
      console.log(`📥 DOWNLOAD: File name: ${file.originalFileName}, Size: ${file.fileSize}`);
    }

    if (!file) {
      // Log failed download attempt
      await activityLogger.logFileOperation(
        userId,
        UserActivityLogModel.ACTION_TYPES.FILE_DOWNLOAD,
        fileId,
        null,
        {
          status: UserActivityLogModel.STATUS_TYPES.FAILURE,
          errorCode: 'FILE_NOT_FOUND',
          errorMessage: 'File not found or access denied',
          requestMetadata: {
            method: req.method,
            url: req.originalUrl,
            userAgent: req.get('User-Agent'),
            ipAddress: req.ip || req.connection?.remoteAddress || '127.0.0.1',
            responseTime: Date.now() - startTime
          }
        }
      );

      return res.status(404).json({
        code: CONSTANTS.CODE.NOT_FOUND,
        message: 'File not found or access denied'
      });
    }

    console.log(`📥 DOWNLOAD: Getting file from Telegram: ${file.telegramFileId}`);

    try {
      // Get file buffer directly from Telegram instead of streaming
      const fileBuffer = await telegramService.downloadFile(file.telegramFileId);

      console.log(`📥 DOWNLOAD: File buffer size: ${fileBuffer.length} bytes`);
      console.log(`📥 DOWNLOAD: Expected size: ${file.fileSize} bytes`);

      // Set response headers
      res.setHeader('Content-Type', file.mimeType || 'application/octet-stream');
      res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(file.originalFileName)}"`);
      res.setHeader('Content-Length', fileBuffer.length);
      res.setHeader('Cache-Control', 'no-cache');

      // Send the buffer directly
      res.send(fileBuffer);

      console.log(`📥 DOWNLOAD SUCCESS: ${file.originalFileName}`);

      // Log successful download
      await activityLogger.logFileOperation(
        userId,
        UserActivityLogModel.ACTION_TYPES.FILE_DOWNLOAD,
        fileId,
        file.originalFileName,
        {
          status: UserActivityLogModel.STATUS_TYPES.SUCCESS,
          fileSize: file.fileSize,
          mimeType: file.mimeType,
          requestMetadata: {
            method: req.method,
            url: req.originalUrl,
            userAgent: req.get('User-Agent'),
            ipAddress: req.ip || req.connection?.remoteAddress || '127.0.0.1',
            responseTime: Date.now() - startTime,
            responseSize: fileBuffer.length
          },
          additionalData: {
            downloadMethod: 'telegram',
            cacheHit: false // Could be enhanced to track cache usage
          }
        }
      );

      // Record recent access for download
      try {
        await RecentAccessModel.recordAccess(userId, file, 'download');
        console.log(`📋 Recent access recorded: ${file.originalFileName} (download)`);
      } catch (recentAccessError) {
        console.warn('Failed to record recent access:', recentAccessError.message);
      }

    } catch (telegramError) {
      console.error('📥 DOWNLOAD ERROR: Telegram download failed:', telegramError);

      // Log telegram download error
      const userId = req.user ? req.user.id : null;
      if (userId && file) {
        await activityLogger.logError(
          userId,
          UserActivityLogModel.ACTION_TYPES.FILE_DOWNLOAD,
          telegramError,
          {
            fileName: file.originalFileName,
            fileId: file._id,
            errorType: 'TELEGRAM_DOWNLOAD_ERROR',
            requestMetadata: {
              method: req.method,
              url: req.originalUrl,
              userAgent: req.get('User-Agent'),
              ipAddress: req.ip || req.connection?.remoteAddress || '127.0.0.1',
              responseTime: Date.now() - startTime
            }
          }
        );
      }

      return res.status(500).json({
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: 'Failed to download file from storage'
      });
    }

  } catch (error) {
    console.error('📥 DOWNLOAD ERROR: General error:', error);

    // Log general download error
    const userId = req.user ? req.user.id : null;
    if (userId) {
      await activityLogger.logError(
        userId,
        UserActivityLogModel.ACTION_TYPES.FILE_DOWNLOAD,
        error,
        {
          fileName: file?.originalFileName,
          fileId: req.params.fileId,
          requestMetadata: {
            method: req.method,
            url: req.originalUrl,
            userAgent: req.get('User-Agent'),
            ipAddress: req.ip || req.connection?.remoteAddress || '127.0.0.1',
            responseTime: Date.now() - startTime
          }
        }
      );
    }

    if (!res.headersSent) {
      res.status(500).json({
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: 'Failed to download file'
      });
    }
  }
};
