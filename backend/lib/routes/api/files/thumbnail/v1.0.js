const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const telegramService = require('../../../../services/telegram');
const cacheService = require('../../../../services/cache');

module.exports = async (req, res) => {
  try {
    const fileId = req.params.fileId;
    console.log(`🖼️ THUMBNAIL REQUEST: File ID: ${fileId}`);

    if (!fileId) {
      return res.status(400).json({
        code: CONSTANTS.CODE.INVALID_PARAMS,
        message: 'File ID is required'
      });
    }

    const FileModel = require('../../../../models/file');
    const userId = req.user ? req.user.id : null;

    const file = await FileModel.findOne({
      _id: fileId,
      isDeleted: false,
      ...(userId && { ownerId: userId }) // Only show user's own files if authenticated
    }).lean();

    if (!file) {
      return res.status(404).json({
        code: CONSTANTS.CODE.NOT_FOUND,
        message: 'File not found'
      });
    }

    console.log(`🖼️ THUMBNAIL FILE INFO:`);
    console.log(`   - Name: ${file.originalFileName}`);
    console.log(`   - MIME Type: ${file.mimeType}`);
    console.log(`   - Has Thumbnail: ${!!file.fileMetadata?.thumbnail}`);

    // Check if file has thumbnail
    if (!file.fileMetadata?.thumbnail) {
      return res.status(404).json({
        code: CONSTANTS.CODE.NOT_FOUND,
        message: 'No thumbnail available for this file'
      });
    }

    const thumbnailFileId = file.fileMetadata.thumbnail;
    console.log(`🖼️ Thumbnail File ID: ${thumbnailFileId}`);

    // Get thumbnail URL from Telegram (use separate cache key for thumbnails)
    const cacheKey = cacheService.getTelegramUrlKey(`thumb_${thumbnailFileId}`);
    let thumbnailUrl = await cacheService.get(cacheKey);

    if (!thumbnailUrl) {
      // Get fresh URL from Telegram
      thumbnailUrl = await telegramService.getFileUrl(thumbnailFileId);

      // Cache URL for 30 minutes (Telegram URLs expire after ~1 hour)
      try {
        await cacheService.set(cacheKey, thumbnailUrl, 1800);
      } catch (cacheError) {
        console.warn('Cache set failed:', cacheError.message);
      }
    }

    console.log(`🖼️ Thumbnail URL: ${thumbnailUrl}`);

    // Set appropriate headers for thumbnail
    res.setHeader('Content-Type', 'image/jpeg'); // Telegram thumbnails are usually JPEG
    res.setHeader('Cache-Control', 'public, max-age=3600'); // Cache for 1 hour
    res.setHeader('X-Content-Type-Options', 'nosniff');

    // Get thumbnail stream from Telegram
    const thumbnailStream = await telegramService.getFileStream(thumbnailFileId);

    // Pipe the stream to response
    thumbnailStream.pipe(res);

    // Handle stream events
    thumbnailStream.on('error', (error) => {
      console.error('Thumbnail stream error:', error);
      if (!res.headersSent) {
        res.status(500).json({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: 'Failed to load thumbnail'
        });
      }
    });

    thumbnailStream.on('end', () => {
      console.log(`🖼️ Thumbnail served: ${file.originalFileName}, ID: ${file._id}`);
    });

  } catch (error) {
    console.error('Thumbnail error:', error);
    if (!res.headersSent) {
      res.status(500).json({
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: 'Failed to load thumbnail'
      });
    }
  }
};
