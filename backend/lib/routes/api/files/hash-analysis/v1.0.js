const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const metadataUtil = require('../../../../util/metadata');

module.exports = async (req, res) => {
  try {
    const { fileIds } = req.body;
    const userId = req.user ? req.user.id : null;

    if (!userId) {
      return res.status(401).json({
        code: CONSTANTS.CODE.UNAUTHORIZED,
        message: 'Authentication required'
      });
    }

    if (!fileIds || !Array.isArray(fileIds) || fileIds.length < 2) {
      return res.status(400).json({
        code: CONSTANTS.CODE.INVALID_PARAMS,
        message: 'At least 2 file IDs are required for comparison'
      });
    }

    if (fileIds.length > 10) {
      return res.status(400).json({
        code: CONSTANTS.CODE.INVALID_PARAMS,
        message: 'Maximum 10 files can be compared at once'
      });
    }

    const FileModel = require('../../../../models/file');

    // Fetch all files
    const files = await FileModel.find({
      _id: { $in: fileIds },
      isDeleted: false,
      ownerId: userId
    }).select('_id originalFileName fileSize uploadDate fileHash uploadMetadata.deviceInfo.platform uploadMetadata.uploadSession.uploadSource').lean();

    if (files.length !== fileIds.length) {
      return res.status(404).json({
        code: CONSTANTS.CODE.NOT_FOUND,
        message: 'One or more files not found or access denied'
      });
    }

    // Perform pairwise hash comparisons
    const comparisons = [];
    const hashGroups = {};

    for (let i = 0; i < files.length; i++) {
      for (let j = i + 1; j < files.length; j++) {
        const file1 = files[i];
        const file2 = files[j];
        
        const comparison = metadataUtil.compareFileHashes(
          file1.fileHash || {},
          file2.fileHash || {}
        );

        comparisons.push({
          file1: {
            id: file1._id,
            name: file1.originalFileName,
            size: file1.fileSize,
            uploadDate: file1.uploadDate,
            hash: file1.fileHash,
            platform: file1.uploadMetadata?.deviceInfo?.platform || 'unknown',
            source: file1.uploadMetadata?.uploadSession?.uploadSource || 'unknown'
          },
          file2: {
            id: file2._id,
            name: file2.originalFileName,
            size: file2.fileSize,
            uploadDate: file2.uploadDate,
            hash: file2.fileHash,
            platform: file2.uploadMetadata?.deviceInfo?.platform || 'unknown',
            source: file2.uploadMetadata?.uploadSession?.uploadSource || 'unknown'
          },
          comparison: comparison
        });

        // Group files by hash similarity
        const groupKey = comparison.isIdentical ? 'identical' : 
                        (comparison.md5Match || comparison.sha256Match) ? 'partial' : 'different';
        
        if (!hashGroups[groupKey]) {
          hashGroups[groupKey] = [];
        }
        hashGroups[groupKey].push({
          file1Id: file1._id,
          file2Id: file2._id,
          comparison: comparison
        });
      }
    }

    // Generate summary statistics
    const summary = {
      totalFiles: files.length,
      totalComparisons: comparisons.length,
      identicalPairs: hashGroups.identical?.length || 0,
      partialMatchPairs: hashGroups.partial?.length || 0,
      differentPairs: hashGroups.different?.length || 0,
      platforms: [...new Set(files.map(f => f.uploadMetadata?.deviceInfo?.platform || 'unknown'))],
      sources: [...new Set(files.map(f => f.uploadMetadata?.uploadSession?.uploadSource || 'unknown'))]
    };

    global.logger.logInfo(`Hash analysis completed for ${files.length} files: ${summary.identicalPairs} identical, ${summary.partialMatchPairs} partial matches, ${summary.differentPairs} different`);

    return res.json({
      code: CONSTANTS.CODE.SUCCESS,
      data: {
        files: files.map(f => ({
          id: f._id,
          name: f.originalFileName,
          size: f.fileSize,
          uploadDate: f.uploadDate,
          hash: f.fileHash,
          platform: f.uploadMetadata?.deviceInfo?.platform || 'unknown',
          source: f.uploadMetadata?.uploadSession?.uploadSource || 'unknown'
        })),
        comparisons: comparisons,
        hashGroups: hashGroups,
        summary: summary
      }
    });

  } catch (error) {
    global.logger.logError(`Error performing hash analysis: ${error.message}`);
    console.error('Hash analysis error:', error);

    return res.status(500).json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });
  }
};
