const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

module.exports = async (req, res) => {
  try {
    const fileId = req.params.fileId;
    console.log(`📋 FILE INFO REQUEST: File ID: ${fileId}`);

    if (!fileId) {
      return res.status(400).json({
        code: CONSTANTS.CODE.INVALID_PARAMS,
        message: 'File ID is required'
      });
    }

    const FileModel = require('../../../../models/file');
    const userId = req.user ? req.user.id : null;

    const file = await FileModel.findOne({
      _id: fileId,
      isDeleted: false,
      ...(userId && { ownerId: userId }) // Only show user's own files if authenticated
    }).lean();

    if (!file) {
      return res.status(404).json({
        code: CONSTANTS.CODE.NOT_FOUND,
        message: 'File not found'
      });
    }

    // Determine file type category
    const getFileCategory = (mimeType) => {
      const mime = mimeType.toLowerCase();
      if (mime.includes('word') || mime.includes('document')) {
        return { category: 'word', icon: '📝', color: '#2196F3', displayType: 'Word Document' };
      } else if (mime.includes('excel') || mime.includes('spreadsheet')) {
        return { category: 'excel', icon: '📊', color: '#4CAF50', displayType: 'Excel Spreadsheet' };
      } else if (mime.includes('powerpoint') || mime.includes('presentation')) {
        return { category: 'powerpoint', icon: '📽️', color: '#FF9800', displayType: 'PowerPoint Presentation' };
      } else if (mime === 'application/pdf') {
        return { category: 'pdf', icon: '📄', color: '#F44336', displayType: 'PDF Document' };
      } else if (mime.startsWith('image/')) {
        return { category: 'image', icon: '🖼️', color: '#9C27B0', displayType: 'Image' };
      } else if (mime.startsWith('video/')) {
        return { category: 'video', icon: '🎬', color: '#E91E63', displayType: 'Video' };
      } else if (mime.startsWith('audio/')) {
        return { category: 'audio', icon: '🎵', color: '#9C27B0', displayType: 'Audio' };
      } else if (mime.startsWith('text/')) {
        return { category: 'text', icon: '📝', color: '#607D8B', displayType: 'Text Document' };
      }
      return { category: 'other', icon: '📄', color: '#757575', displayType: 'Document' };
    };

    const fileTypeInfo = getFileCategory(file.mimeType);

    // Check if file is previewable by external viewers
    const isOfficeFile = file.mimeType.includes('word') ||
                        file.mimeType.includes('excel') ||
                        file.mimeType.includes('powerpoint') ||
                        file.mimeType.includes('document') ||
                        file.mimeType.includes('spreadsheet') ||
                        file.mimeType.includes('presentation') ||
                        file.mimeType.includes('officedocument');

    const previewOptions = {
      canPreviewInline: ['image', 'video', 'audio', 'pdf', 'text'].includes(fileTypeInfo.category),
      canPreviewWithGoogleDocs: isOfficeFile || file.mimeType === 'application/pdf',
      canPreviewWithMicrosoftOffice: isOfficeFile,
      requiresDownload: !['image', 'video', 'audio', 'pdf', 'text'].includes(fileTypeInfo.category) && !isOfficeFile
    };

    // Format file size
    const formatFileSize = (bytes) => {
      if (bytes === 0) return '0 Bytes';
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    const fileInfo = {
      id: file._id,
      name: file.originalFileName,
      size: file.fileSize,
      formattedSize: formatFileSize(file.fileSize),
      mimeType: file.mimeType,
      uploadDate: file.uploadDate,
      parentId: file.parentId,
      telegramFileId: file.telegramFileId,
      ...fileTypeInfo,
      previewOptions,
      // Add metadata information
      uploadMetadata: file.uploadMetadata || {},
      fileHash: file.fileHash || {}
    };

    console.log(`📋 FILE INFO RESPONSE:`, {
      name: fileInfo.name,
      category: fileInfo.category,
      previewOptions: fileInfo.previewOptions
    });

    res.json({
      code: CONSTANTS.CODE.SUCCESS,
      message: 'File info retrieved successfully',
      data: fileInfo
    });

  } catch (error) {
    console.error('File info error:', error);
    res.status(500).json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: 'Failed to get file info'
    });
  }
};
