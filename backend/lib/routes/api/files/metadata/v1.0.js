const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

module.exports = async (req, res) => {
  try {
    const fileId = req.params.fileId;
    console.log(`📊 FILE METADATA REQUEST: File ID: ${fileId}`);

    if (!fileId) {
      return res.status(400).json({
        code: CONSTANTS.CODE.INVALID_PARAMS,
        message: 'File ID is required'
      });
    }

    const FileModel = require('../../../../models/file');
    const userId = req.user ? req.user.id : null;

    if (!userId) {
      return res.status(401).json({
        code: CONSTANTS.CODE.UNAUTHORIZED,
        message: 'Authentication required'
      });
    }

    const file = await FileModel.findOne({
      _id: fileId,
      isDeleted: false,
      ownerId: userId // Only allow user's own files
    }).lean();

    if (!file) {
      return res.status(404).json({
        code: CONSTANTS.CODE.NOT_FOUND,
        message: 'File not found or access denied'
      });
    }

    // Format upload duration
    const formatDuration = (ms) => {
      if (!ms) return 'Unknown';
      if (ms < 1000) return `${ms}ms`;
      if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
      return `${(ms / 60000).toFixed(1)}m`;
    };

    // Format upload speed
    const formatSpeed = (bytesPerSecond) => {
      if (!bytesPerSecond) return 'Unknown';
      if (bytesPerSecond < 1024) return `${bytesPerSecond} B/s`;
      if (bytesPerSecond < 1024 * 1024) return `${(bytesPerSecond / 1024).toFixed(1)} KB/s`;
      return `${(bytesPerSecond / (1024 * 1024)).toFixed(1)} MB/s`;
    };

    // Format file size
    const formatFileSize = (bytes) => {
      if (bytes === 0) return '0 Bytes';
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    // Prepare comprehensive metadata response
    const metadata = {
      // Basic file information
      fileInfo: {
        id: file._id,
        name: file.originalFileName,
        size: file.fileSize,
        formattedSize: formatFileSize(file.fileSize),
        mimeType: file.mimeType,
        uploadDate: file.uploadDate,
        telegramFileId: file.telegramFileId
      },

      // File hashes
      fileHash: file.fileHash || {},

      // Upload session details
      uploadSession: file.uploadMetadata?.uploadSession ? {
        sessionId: file.uploadMetadata.uploadSession.sessionId,
        uploadStartTime: file.uploadMetadata.uploadSession.uploadStartTime,
        uploadEndTime: file.uploadMetadata.uploadSession.uploadEndTime,
        uploadDuration: file.uploadMetadata.uploadSession.uploadDuration,
        formattedDuration: formatDuration(file.uploadMetadata.uploadSession.uploadDuration),
        uploadSpeed: file.uploadMetadata.uploadSession.uploadSpeed,
        formattedSpeed: formatSpeed(file.uploadMetadata.uploadSession.uploadSpeed),
        retryCount: file.uploadMetadata.uploadSession.retryCount || 0,
        uploadSource: file.uploadMetadata.uploadSession.uploadSource || 'unknown'
      } : null,

      // Client information
      clientInfo: file.uploadMetadata ? {
        ipAddress: file.uploadMetadata.ipAddress,
        userAgent: file.uploadMetadata.userAgent,
        referer: file.uploadMetadata.referer
      } : null,

      // Geolocation information
      geolocation: file.uploadMetadata?.geolocation ? {
        latitude: file.uploadMetadata.geolocation.latitude,
        longitude: file.uploadMetadata.geolocation.longitude,
        accuracy: file.uploadMetadata.geolocation.accuracy,
        timestamp: file.uploadMetadata.geolocation.timestamp,
        address: file.uploadMetadata.geolocation.address,
        city: file.uploadMetadata.geolocation.city,
        country: file.uploadMetadata.geolocation.country,
        hasLocation: !!(file.uploadMetadata.geolocation.latitude && file.uploadMetadata.geolocation.longitude)
      } : null,

      // Device information
      deviceInfo: file.uploadMetadata?.deviceInfo ? {
        platform: file.uploadMetadata.deviceInfo.platform,
        browser: file.uploadMetadata.deviceInfo.browser,
        browserVersion: file.uploadMetadata.deviceInfo.browserVersion,
        isMobile: file.uploadMetadata.deviceInfo.isMobile,
        isTablet: file.uploadMetadata.deviceInfo.isTablet,
        screenResolution: file.uploadMetadata.deviceInfo.screenResolution,
        timezone: file.uploadMetadata.deviceInfo.timezone,
        language: file.uploadMetadata.deviceInfo.language
      } : null,

      // Network information
      networkInfo: file.uploadMetadata?.networkInfo ? {
        connectionType: file.uploadMetadata.networkInfo.connectionType,
        downlink: file.uploadMetadata.networkInfo.downlink,
        effectiveType: file.uploadMetadata.networkInfo.effectiveType,
        rtt: file.uploadMetadata.networkInfo.rtt
      } : null,

      // Telegram metadata
      telegramMetadata: file.telegramMetadata || {},

      // File-specific metadata
      fileMetadata: file.fileMetadata || {},

      // EXIF data from images/videos
      exifData: file.exifData || {},

      // Additional image metadata
      imageMetadata: file.imageMetadata || {}
    };

    console.log(`📊 FILE METADATA RESPONSE:`, {
      name: metadata.fileInfo.name,
      hasUploadMetadata: !!file.uploadMetadata,
      hasGeolocation: !!(file.uploadMetadata?.geolocation?.latitude),
      uploadSource: file.uploadMetadata?.uploadSession?.uploadSource
    });

    res.json({
      code: CONSTANTS.CODE.SUCCESS,
      message: 'File metadata retrieved successfully',
      data: metadata
    });

  } catch (error) {
    console.error('File metadata error:', error);
    res.status(500).json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: 'Failed to get file metadata'
    });
  }
};
