const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const metadataUtil = require('../../../../util/metadata');
const fs = require('fs');

module.exports = async (req, res) => {
  try {
    const { md5, sha256, parentId } = req.body;
    const userId = req.user ? req.user.id : null;

    if (!userId) {
      return res.status(401).json({
        code: CONSTANTS.CODE.UNAUTHORIZED,
        message: 'Authentication required'
      });
    }

    if (!md5 && !sha256) {
      return res.status(400).json({
        code: CONSTANTS.CODE.INVALID_PARAMS,
        message: 'At least one hash (MD5 or SHA256) is required'
      });
    }

    // Check for duplicates in the specified folder
    const duplicateFile = await metadataUtil.checkForDuplicates(
      md5, 
      sha256, 
      userId, 
      parentId || null
    );

    if (duplicateFile) {
      // Compare hashes for detailed analysis
      const hashComparison = metadataUtil.compareFileHashes(
        { md5, sha256 },
        duplicateFile.fileHash || {}
      );

      global.logger.logInfo(`Duplicate check found match: ${duplicateFile.originalFileName} (ID: ${duplicateFile._id})`);

      return res.json({
        code: CONSTANTS.CODE.SUCCESS,
        data: {
          isDuplicate: true,
          duplicateFile: {
            id: duplicateFile._id,
            originalFileName: duplicateFile.originalFileName,
            fileSize: duplicateFile.fileSize,
            uploadDate: duplicateFile.uploadDate,
            fileHash: duplicateFile.fileHash,
            parentId: duplicateFile.parentId
          },
          hashComparison: hashComparison,
          message: 'A file with identical content already exists in this folder'
        }
      });
    } else {
      return res.json({
        code: CONSTANTS.CODE.SUCCESS,
        data: {
          isDuplicate: false,
          message: 'No duplicate file found in this folder'
        }
      });
    }

  } catch (error) {
    global.logger.logError(`Error checking for duplicates: ${error.message}`);
    console.error('Duplicate check error:', error);

    return res.status(500).json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });
  }
};
