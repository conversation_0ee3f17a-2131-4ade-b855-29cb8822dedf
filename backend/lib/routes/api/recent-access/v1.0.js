const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const RecentAccessModel = require('../../../models/recentAccess');

module.exports = async (req, res) => {
  try {
    const userId = req.user ? req.user.id : null;

    if (!userId) {
      return res.status(401).json({
        code: CONSTANTS.CODE.UNAUTHORIZED,
        message: 'User authentication required'
      });
    }

    // Get query parameters
    const limit = parseInt(req.query.limit) || 20;
    const offset = parseInt(req.query.offset) || 0;
    const accessType = req.query.accessType; // Optional filter: 'view', 'download', 'preview'

    // Validate limit (max 50 to prevent abuse)
    const maxLimit = Math.min(limit, 50);

    console.log(`📋 RECENT ACCESS REQUEST: User: ${userId}, Limit: ${maxLimit}, Offset: ${offset}, AccessType: ${accessType || 'all'}`);

    // Get recent access records
    let recentFiles;

    if (accessType && ['view', 'download', 'preview'].includes(accessType)) {
      // Filter by access type
      recentFiles = await RecentAccessModel.find({
        userId: userId,
        accessType: accessType
      })
      .sort({ accessedAt: -1 })
      .limit(maxLimit)
      .skip(offset)
      .populate('fileId', 'originalFileName fileSize mimeType isDeleted parentId telegramFileId fileMetadata')
      .lean();
    } else {
      // Get all recent access
      recentFiles = await RecentAccessModel.getRecentAccess(userId, maxLimit, offset);
    }

    // Transform data for frontend
    const transformedFiles = recentFiles
      .filter(access => access.fileId && !access.fileId.isDeleted) // Double-check for deleted files
      .map(access => ({
        id: access.fileId._id,
        name: access.fileName,
        type: 'file',
        size: access.fileSize,
        mimeType: access.mimeType,
        fileType: access.fileType,
        uploadDate: access.fileId.uploadDate || access.accessedAt,
        parentId: access.parentFolderId,
        accessedAt: access.accessedAt,
        accessType: access.accessType,
        accessCount: access.accessCount,
        // Additional metadata for better UX
        originalFileName: access.fileId.originalFileName,
        telegramFileId: access.fileId.telegramFileId,
        // Thumbnail information for better UI
        hasThumbnail: !!(access.fileId.fileMetadata && access.fileId.fileMetadata.thumbnail),
        thumbnailId: access.fileId.fileMetadata && access.fileId.fileMetadata.thumbnail ? access.fileId.fileMetadata.thumbnail : null,
        dimensions: access.fileId.fileMetadata && (access.fileId.fileMetadata.width || access.fileId.fileMetadata.height) ? {
          width: access.fileId.fileMetadata.width,
          height: access.fileId.fileMetadata.height
        } : null
      }));

    // Get total count for pagination
    const totalCount = await RecentAccessModel.countDocuments({
      userId: userId,
      ...(accessType && ['view', 'download', 'preview'].includes(accessType) && { accessType })
    });

    console.log(`📋 RECENT ACCESS RESPONSE: Found ${transformedFiles.length} files, Total: ${totalCount}`);

    return res.json({
      code: CONSTANTS.CODE.SUCCESS,
      data: {
        files: transformedFiles,
        pagination: {
          total: totalCount,
          limit: maxLimit,
          offset: offset,
          hasMore: (offset + maxLimit) < totalCount
        }
      },
      message: 'Recent access retrieved successfully'
    });

  } catch (error) {
    global.logger.logError(['recent-access error', error.message], __dirname);
    console.error('Recent access error:', error);

    return res.status(500).json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: error.message || MESSAGES.SYSTEM.ERROR
    });
  }
};
