const config = require('../../../../../config');
const CONSTANTS = require('../../../../../const');

module.exports = (req, res) => {
  try {
    const oauthConfig = {
      enabled: config.get('oauth.enabled'),
      google: {
        enabled: config.get('oauth.google.enabled')
      },
      telegram: {
        enabled: config.get('oauth.telegram.enabled')
      }
    };

    res.json({
      code: CONSTANTS.CODE.SUCCESS,
      data: oauthConfig,
      message: 'OAuth configuration retrieved successfully'
    });
  } catch (error) {
    console.error('OAuth config error:', error);
    res.status(500).json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: 'Failed to retrieve OAuth configuration'
    });
  }
};
