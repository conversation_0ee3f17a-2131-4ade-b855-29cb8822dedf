const passport = require('passport');
const config = require('../../../../../config');
const CONSTANTS = require('../../../../../const');
const MESSAGES = require('../../../../../message');

module.exports = {
  // Initiate Google OAuth
  login: (req, res, next) => {
    if (!config.get('oauth.enabled') || !config.get('oauth.google.enabled')) {
      return res.status(400).json({
        code: CONSTANTS.CODE.INVALID_PARAMS,
        message: 'Google OAuth is not enabled'
      });
    }

    passport.authenticate('google', {
      scope: ['profile', 'email']
    })(req, res, next);
  },

  // Handle Google OAuth callback
  callback: (req, res, next) => {
    if (!config.get('oauth.enabled') || !config.get('oauth.google.enabled')) {
      return res.status(400).json({
        code: CONSTANTS.CODE.INVALID_PARAMS,
        message: 'Google OAuth is not enabled'
      });
    }

    passport.authenticate('google', {
      failureRedirect: '/login?error=google_auth_failed',
      session: false
    }, async (err, user, info) => {
      if (err) {
        console.error('Google OAuth callback error:', err);
        return res.redirect('/login?error=oauth_error');
      }

      if (!user) {
        return res.redirect('/login?error=google_auth_failed');
      }

      try {
        // Generate JWT token
        const jwt = require('jsonwebtoken');
        const redisConnection = require('../../../../../connections/redis');

        const token = jwt.sign({
          username: user.username,
          id: user._id
        }, config.get('secretKey'));

        const userId = user._id.toString();
        const objSign = {
          id: userId,
          role: user.role,
        };

        // Store token in Redis
        await new Promise((resolve, reject) => {
          redisConnection('master')
            .getConnection()
            .multi()
            .set(`user:${userId}`, token)
            .set(`user:${token}`, JSON.stringify(objSign))
            .exec((err, result) => {
              if (err) reject(err);
              else resolve(result);
            });
        });

        // Redirect to frontend with token
        res.redirect(`/login-success?token=${token}&user=${encodeURIComponent(JSON.stringify({
          id: user._id,
          username: user.username,
          email: user.email,
          name: user.name,
          avatar: user.avatar,
          provider: user.provider
        }))}`);

      } catch (error) {
        console.error('Token generation error:', error);
        res.redirect('/login?error=token_error');
      }
    })(req, res, next);
  }
};
