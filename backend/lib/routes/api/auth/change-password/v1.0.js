const _ = require('lodash');
const bcrypt = require('bcryptjs');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const UserModel = require('../../../../models/user');

module.exports = async (req, res) => {
  try {
    const userId = _.get(req, 'user.id');
    const { currentPassword, newPassword, confirmPassword } = req.body;

    // Check if user is authenticated
    if (!userId) {
      return res.status(401).json({
        code: CONSTANTS.CODE.UNAUTHORIZED,
        message: 'User authentication required'
      });
    }

    // Check required params
    if (!currentPassword || !newPassword || !confirmPassword) {
      return res.status(400).json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: 'Current password, new password, and confirm password are required'
      });
    }

    // Check if new passwords match
    if (newPassword !== confirmPassword) {
      return res.status(400).json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: 'New password and confirm password do not match'
      });
    }

    // Validate new password strength
    if (newPassword.length < 6) {
      return res.status(400).json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: 'New password must be at least 6 characters long'
      });
    }

    // Find user
    const user = await UserModel.findOne({
      _id: userId,
      status: 1
    });

    if (!user) {
      return res.status(404).json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.NOT_EXISTS
      });
    }

    // Check if user is local provider (has password)
    if (user.provider !== 'local' || !user.password) {
      return res.status(400).json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: 'Password change is only available for local accounts'
      });
    }

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
    if (!isCurrentPasswordValid) {
      return res.status(400).json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: 'Current password is incorrect'
      });
    }

    // Check if new password is different from current password
    const isSamePassword = await bcrypt.compare(newPassword, user.password);
    if (isSamePassword) {
      return res.status(400).json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: 'New password must be different from current password'
      });
    }

    // Hash new password
    const hashedNewPassword = await bcrypt.hash(newPassword, 10);

    // Update user password
    await UserModel.updateOne(
      { _id: userId },
      {
        password: hashedNewPassword,
        updatedAt: Date.now()
      }
    );

    res.json({
      code: CONSTANTS.CODE.SUCCESS,
      message: 'Password changed successfully'
    });

  } catch (error) {
    console.error('Change password error:', error);
    res.status(500).json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });
  }
};
