const _ = require('lodash');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const config = require('../../../../config');
const redisConnection = require('../../../../connections/redis');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const UserModel = require('../../../../models/user');
const activityLogger = require('../../../../services/activityLogger');
const UserActivityLogModel = require('../../../../models/userActivityLog');

module.exports = async (req, res) => {
  const startTime = Date.now();
  let userId = null;

  try {
    const { username, password } = req.body;

    // Check params
    if (!username.trim() || !password.trim()) {
      // Log failed login attempt
      await activityLogger.logAuth(
        null,
        UserActivityLogModel.ACTION_TYPES.LOGIN,
        UserActivityLogModel.STATUS_TYPES.FAILURE,
        {
          provider: 'local',
          method: 'password',
          errorCode: 'MISSING_PARAMS',
          errorMessage: 'Missing username or password',
          requestMetadata: {
            method: req.method,
            url: req.originalUrl,
            userAgent: req.get('User-Agent'),
            ipAddress: req.ip || req.connection?.remoteAddress || '127.0.0.1',
            responseTime: Date.now() - startTime
          }
        }
      );

      return res.status(400).json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    // Find user
    const user = await UserModel.findOne({
      username: username.trim().toLowerCase(),
      status: 1
    }).lean();

    if (!user) {
      // Log failed login attempt
      await activityLogger.logAuth(
        null,
        UserActivityLogModel.ACTION_TYPES.LOGIN,
        UserActivityLogModel.STATUS_TYPES.FAILURE,
        {
          provider: 'local',
          method: 'password',
          errorCode: 'USER_NOT_FOUND',
          errorMessage: 'User not found or inactive',
          attemptedUsername: username.trim().toLowerCase(),
          requestMetadata: {
            method: req.method,
            url: req.originalUrl,
            userAgent: req.get('User-Agent'),
            ipAddress: req.ip || req.connection?.remoteAddress || '127.0.0.1',
            responseTime: Date.now() - startTime
          }
        }
      );

      return res.status(400).json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.NOT_EXISTS
      });
    }

    userId = user._id.toString();

    // Check if user is OAuth user without password
    if (!user.password && user.provider !== 'local') {
      // Log failed login attempt
      await activityLogger.logAuth(
        userId,
        UserActivityLogModel.ACTION_TYPES.LOGIN,
        UserActivityLogModel.STATUS_TYPES.FAILURE,
        {
          provider: user.provider,
          method: 'password',
          errorCode: 'OAUTH_USER_PASSWORD_LOGIN',
          errorMessage: 'OAuth user attempted password login',
          requestMetadata: {
            method: req.method,
            url: req.originalUrl,
            userAgent: req.get('User-Agent'),
            ipAddress: req.ip || req.connection?.remoteAddress || '127.0.0.1',
            responseTime: Date.now() - startTime
          }
        }
      );

      return res.status(400).json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: 'Please use OAuth login for this account'
      });
    }

    // Check if email is verified for local users
    if (user.provider === 'local' && !user.isEmailVerified) {
      // Log failed login attempt
      await activityLogger.logAuth(
        userId,
        UserActivityLogModel.ACTION_TYPES.LOGIN,
        UserActivityLogModel.STATUS_TYPES.FAILURE,
        {
          provider: 'local',
          method: 'password',
          errorCode: 'EMAIL_NOT_VERIFIED',
          errorMessage: 'Email not verified',
          requestMetadata: {
            method: req.method,
            url: req.originalUrl,
            userAgent: req.get('User-Agent'),
            ipAddress: req.ip || req.connection?.remoteAddress || '127.0.0.1',
            responseTime: Date.now() - startTime
          }
        }
      );

      return res.status(400).json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: 'Please verify your email before logging in. Check your email for verification link.'
      });
    }

    // Check password
    const isMatch = await bcrypt.compare(password.trim(), user.password);

    if (!isMatch) {
      // Log failed login attempt
      await activityLogger.logAuth(
        userId,
        UserActivityLogModel.ACTION_TYPES.LOGIN,
        UserActivityLogModel.STATUS_TYPES.FAILURE,
        {
          provider: 'local',
          method: 'password',
          errorCode: 'INCORRECT_PASSWORD',
          errorMessage: 'Incorrect password',
          requestMetadata: {
            method: req.method,
            url: req.originalUrl,
            userAgent: req.get('User-Agent'),
            ipAddress: req.ip || req.connection?.remoteAddress || '127.0.0.1',
            responseTime: Date.now() - startTime
          }
        }
      );

      return res.status(400).json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.INCORRECT_PASSWORD
      });
    }

    // Create new token
    const token = jwt.sign({ username: username.trim().toLowerCase(), id: user._id }, config.secretKey);

    // Store in Redis using async API
    const objSign = {
      id: userId,
      role: user.role,
    };

    try {
      const redis = redisConnection('master').getConnection();

      // Redis v4+ uses async/await
      await redis.set(`user:${userId}`, token);
      await redis.set(`user:${token}`, JSON.stringify(objSign));

      console.log('✅ Token stored in Redis successfully');
    } catch (redisError) {
      console.error('Redis storage error:', redisError);
      // Continue anyway - token will still work for this session
    }

    // Update last login time
    await UserModel.updateOne(
      { _id: user._id },
      { lastLoginAt: new Date() }
    );

    // Log successful login
    await activityLogger.logAuth(
      userId,
      UserActivityLogModel.ACTION_TYPES.LOGIN,
      UserActivityLogModel.STATUS_TYPES.SUCCESS,
      {
        provider: user.provider || 'local',
        method: 'password',
        requestMetadata: {
          method: req.method,
          url: req.originalUrl,
          userAgent: req.get('User-Agent'),
          ipAddress: req.ip || req.connection?.remoteAddress || '127.0.0.1',
          responseTime: Date.now() - startTime
        },
        sessionInfo: {
          tokenId: token.substring(0, 16) // Store partial token for identification
        }
      }
    );

    // Send response
    const responseData = _.merge({}, user, { token });
    _.unset(responseData, 'password');

    res.json({
      code: CONSTANTS.CODE.SUCCESS,
      data: responseData,
    });

  } catch (error) {
    console.error('Login error:', error);

    // Log error
    if (userId) {
      await activityLogger.logError(
        userId,
        UserActivityLogModel.ACTION_TYPES.LOGIN,
        error,
        {
          provider: 'local',
          method: 'password',
          requestMetadata: {
            method: req.method,
            url: req.originalUrl,
            userAgent: req.get('User-Agent'),
            ipAddress: req.ip || req.connection?.remoteAddress || '127.0.0.1',
            responseTime: Date.now() - startTime
          }
        }
      );
    }

    res.status(500).json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });
  }
};
