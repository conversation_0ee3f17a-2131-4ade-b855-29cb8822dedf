const _ = require('lodash');
const redisConnection = require('../../../../connections/redis');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const activityLogger = require('../../../../services/activityLogger');
const UserActivityLogModel = require('../../../../models/userActivityLog');

module.exports = async (req, res) => {
  const startTime = Date.now();

  try {
    const token = _.get(req, 'headers.token', '');
    const userId = _.get(req, 'user.id', '');

    if (!token || !userId) {
      // Log failed logout attempt
      await activityLogger.logAuth(
        userId || null,
        UserActivityLogModel.ACTION_TYPES.LOGOUT,
        UserActivityLogModel.STATUS_TYPES.FAILURE,
        {
          errorCode: 'MISSING_TOKEN_OR_USER',
          errorMessage: 'Missing token or user ID',
          requestMetadata: {
            method: req.method,
            url: req.originalUrl,
            userAgent: req.get('User-Agent'),
            ipAddress: req.ip || req.connection?.remoteAddress || '127.0.0.1',
            responseTime: Date.now() - startTime
          }
        }
      );

      return res.status(400).json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    try {
      const redis = redisConnection('master').getConnection();

      // Remove token from Redis
      await redis.del(`user:${userId}`);
      await redis.del(`user:${token}`);

      console.log('✅ Token removed from Redis successfully');
    } catch (redisError) {
      console.error('Redis deletion error:', redisError);
      // Continue anyway - token will expire naturally
    }

    // Log successful logout
    await activityLogger.logAuth(
      userId,
      UserActivityLogModel.ACTION_TYPES.LOGOUT,
      UserActivityLogModel.STATUS_TYPES.SUCCESS,
      {
        requestMetadata: {
          method: req.method,
          url: req.originalUrl,
          userAgent: req.get('User-Agent'),
          ipAddress: req.ip || req.connection?.remoteAddress || '127.0.0.1',
          responseTime: Date.now() - startTime
        },
        sessionInfo: {
          tokenId: token.substring(0, 16) // Store partial token for identification
        }
      }
    );

    res.json({
      code: CONSTANTS.CODE.SUCCESS,
      message: 'Logged out successfully'
    });

  } catch (error) {
    console.error('Logout error:', error);

    // Log error
    const userId = _.get(req, 'user.id', '');
    if (userId) {
      await activityLogger.logError(
        userId,
        UserActivityLogModel.ACTION_TYPES.LOGOUT,
        error,
        {
          requestMetadata: {
            method: req.method,
            url: req.originalUrl,
            userAgent: req.get('User-Agent'),
            ipAddress: req.ip || req.connection?.remoteAddress || '127.0.0.1',
            responseTime: Date.now() - startTime
          }
        }
      );
    }

    res.status(500).json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });
  }
};
