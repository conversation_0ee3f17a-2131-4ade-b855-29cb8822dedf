const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const cacheService = require('../../../../services/cache');

module.exports = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user ? req.user.id : null;

    if (!userId) {
      return res.status(401).json({
        code: CONSTANTS.CODE.UNAUTHORIZED,
        message: 'Authentication required'
      });
    }

    if (!id) {
      return res.status(400).json({
        code: CONSTANTS.CODE.INVALID_PARAMS,
        message: 'Item ID is required'
      });
    }

    const FileModel = require('../../../../models/file');
    const FolderModel = require('../../../../models/folder');

    // Try to find as file first
    let item = await FileModel.findOne({
      _id: id,
      ownerId: userId,
      isDeleted: true,
      permanentlyDeletedAt: { $exists: false }
    });

    let itemType = 'file';
    let deletedCount = 0;

    if (item) {
      // Permanently delete the file
      await FileModel.updateOne(
        { _id: id, ownerId: userId, isDeleted: true },
        {
          $set: {
            permanentlyDeletedAt: new Date()
          }
        }
      );

      // Note: We don't delete from Telegram as files might be referenced elsewhere
      // and Telegram doesn't provide a way to delete files anyway

      deletedCount = 1;
    } else {
      // Try to find as folder
      item = await FolderModel.findOne({
        _id: id,
        ownerId: userId,
        isDeleted: true,
        permanentlyDeletedAt: { $exists: false }
      });

      if (!item) {
        return res.status(404).json({
          code: CONSTANTS.CODE.NOT_FOUND,
          message: 'Item not found in trash'
        });
      }

      itemType = 'folder';

      // Recursively permanently delete folder and its contents
      const deleteResults = await permanentlyDeleteFolder(id, userId);
      deletedCount = deleteResults.totalDeleted;
    }

    // Clear cache
    try {
      await cacheService.clearSearchCache();
    } catch (cacheError) {
      console.warn('Cache clear failed:', cacheError.message);
    }

    global.logger.logInfo(`${itemType} permanently deleted: ID ${id}, ${deletedCount} items deleted`);

    return res.json({
      code: CONSTANTS.CODE.SUCCESS,
      data: {
        id: id,
        type: itemType,
        deletedCount: deletedCount
      },
      message: `${itemType === 'file' ? 'File' : 'Folder'} permanently deleted`
    });

  } catch (error) {
    global.logger.logInfo(['trash/permanent error', error.message], __dirname);
    console.error('Permanent delete error:', error);

    return res.status(500).json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });
  }

  // Helper function to recursively permanently delete folder and its contents
  async function permanentlyDeleteFolder(folderId, userId) {
    const FileModel = require('../../../../models/file');
    const FolderModel = require('../../../../models/folder');

    let totalDeleted = 0;

    // Get all deleted subfolders
    const subfolders = await FolderModel.find({
      parentId: folderId,
      ownerId: userId,
      isDeleted: true,
      permanentlyDeletedAt: { $exists: false }
    }).select('_id').lean();

    // Recursively permanently delete subfolders
    for (const subfolder of subfolders) {
      const subResult = await permanentlyDeleteFolder(subfolder._id, userId);
      totalDeleted += subResult.totalDeleted;
    }

    // Permanently delete all files in this folder
    const fileDeleteResult = await FileModel.updateMany(
      {
        parentId: folderId,
        ownerId: userId,
        isDeleted: true,
        permanentlyDeletedAt: { $exists: false }
      },
      {
        $set: { permanentlyDeletedAt: new Date() }
      }
    );
    totalDeleted += fileDeleteResult.modifiedCount;

    // Permanently delete all subfolders in this folder
    const subfolderDeleteResult = await FolderModel.updateMany(
      {
        parentId: folderId,
        ownerId: userId,
        isDeleted: true,
        permanentlyDeletedAt: { $exists: false }
      },
      {
        $set: { permanentlyDeletedAt: new Date() }
      }
    );
    totalDeleted += subfolderDeleteResult.modifiedCount;

    // Permanently delete the folder itself
    const mainFolderResult = await FolderModel.updateOne(
      {
        _id: folderId,
        ownerId: userId,
        isDeleted: true,
        permanentlyDeletedAt: { $exists: false }
      },
      {
        $set: { permanentlyDeletedAt: new Date() }
      }
    );
    totalDeleted += mainFolderResult.modifiedCount;

    return {
      totalDeleted
    };
  }
};
