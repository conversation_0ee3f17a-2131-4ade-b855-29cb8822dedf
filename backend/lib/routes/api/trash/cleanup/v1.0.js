const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

module.exports = async (req, res) => {
  try {
    const FileModel = require('../../../../models/file');
    const FolderModel = require('../../../../models/folder');

    // Calculate date 30 days ago
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    // Find items deleted more than 30 days ago that haven't been permanently deleted
    const oldDeletedFiles = await FileModel.find({
      isDeleted: true,
      deletedAt: { $lt: thirtyDaysAgo },
      permanentlyDeletedAt: { $exists: false }
    }).select('_id ownerId originalFileName').lean();

    const oldDeletedFolders = await FolderModel.find({
      isDeleted: true,
      deletedAt: { $lt: thirtyDaysAgo },
      permanentlyDeletedAt: { $exists: false }
    }).select('_id ownerId folderName').lean();

    // Permanently delete old files
    const fileCleanupResult = await FileModel.updateMany(
      {
        isDeleted: true,
        deletedAt: { $lt: thirtyDaysAgo },
        permanentlyDeletedAt: { $exists: false }
      },
      {
        $set: { permanentlyDeletedAt: new Date() }
      }
    );

    // Permanently delete old folders
    const folderCleanupResult = await FolderModel.updateMany(
      {
        isDeleted: true,
        deletedAt: { $lt: thirtyDaysAgo },
        permanentlyDeletedAt: { $exists: false }
      },
      {
        $set: { permanentlyDeletedAt: new Date() }
      }
    );

    const totalCleaned = fileCleanupResult.modifiedCount + folderCleanupResult.modifiedCount;

    global.logger.logInfo(`Auto-cleanup completed: ${totalCleaned} items permanently deleted (${fileCleanupResult.modifiedCount} files, ${folderCleanupResult.modifiedCount} folders)`);

    return res.json({
      code: CONSTANTS.CODE.SUCCESS,
      data: {
        totalCleaned: totalCleaned,
        filesCleaned: fileCleanupResult.modifiedCount,
        foldersCleaned: folderCleanupResult.modifiedCount,
        cleanupDate: thirtyDaysAgo.toISOString(),
        cleanedItems: {
          files: oldDeletedFiles.map(f => ({ id: f._id, name: f.originalFileName, owner: f.ownerId })),
          folders: oldDeletedFolders.map(f => ({ id: f._id, name: f.folderName, owner: f.ownerId }))
        }
      },
      message: `Auto-cleanup completed: ${totalCleaned} items permanently deleted`
    });

  } catch (error) {
    global.logger.logInfo(['trash/cleanup error', error.message], __dirname);
    console.error('Auto-cleanup error:', error);

    return res.status(500).json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });
  }
};
