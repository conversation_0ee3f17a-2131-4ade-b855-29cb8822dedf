const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const cacheService = require('../../../../services/cache');
const storageService = require('../../../../services/storageService');

module.exports = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user ? req.user.id : null;

    if (!userId) {
      return res.status(401).json({
        code: CONSTANTS.CODE.UNAUTHORIZED,
        message: 'Authentication required'
      });
    }

    if (!id) {
      return res.status(400).json({
        code: CONSTANTS.CODE.INVALID_PARAMS,
        message: 'Item ID is required'
      });
    }

    const FileModel = require('../../../../models/file');
    const FolderModel = require('../../../../models/folder');

    // Try to find as file first
    let item = await FileModel.findOne({
      _id: id,
      ownerId: userId,
      isDeleted: true,
      permanentlyDeletedAt: { $exists: false }
    });

    let itemType = 'file';
    let restoredCount = 0;

    if (item) {
      // Check if parent folder exists and is not deleted
      if (item.parentId) {
        const parentFolder = await FolderModel.findOne({
          _id: item.parentId,
          isDeleted: false
        });

        if (!parentFolder) {
          // Parent folder doesn't exist or is deleted, move to root
          item.parentId = null;
        }
      }

      // Restore the file
      await FileModel.updateOne(
        { _id: id, ownerId: userId, isDeleted: true },
        {
          $unset: { deletedAt: 1 },
          $set: { 
            isDeleted: false,
            parentId: item.parentId
          }
        }
      );

      // Update user storage usage
      if (item.fileSize) {
        try {
          await storageService.addStorageUsage(userId, item.fileSize);
        } catch (storageError) {
          console.warn('Failed to update storage usage on file restore:', storageError.message);
        }
      }

      restoredCount = 1;
    } else {
      // Try to find as folder
      item = await FolderModel.findOne({
        _id: id,
        ownerId: userId,
        isDeleted: true,
        permanentlyDeletedAt: { $exists: false }
      });

      if (!item) {
        return res.status(404).json({
          code: CONSTANTS.CODE.NOT_FOUND,
          message: 'Item not found in trash'
        });
      }

      itemType = 'folder';

      // Check if parent folder exists and is not deleted
      if (item.parentId) {
        const parentFolder = await FolderModel.findOne({
          _id: item.parentId,
          isDeleted: false
        });

        if (!parentFolder) {
          // Parent folder doesn't exist or is deleted, move to root
          item.parentId = null;
        }
      }

      // Recursively restore folder and its contents
      const restoreResults = await restoreFolder(id, userId);
      restoredCount = restoreResults.totalRestored;
    }

    // Clear cache for parent folder and search
    try {
      await cacheService.clearFolderCache(item.parentId);
      await cacheService.clearSearchCache();
    } catch (cacheError) {
      console.warn('Cache clear failed:', cacheError.message);
    }

    global.logger.logInfo(`${itemType} restored successfully: ID ${id}, ${restoredCount} items restored`);

    return res.json({
      code: CONSTANTS.CODE.SUCCESS,
      data: {
        id: id,
        type: itemType,
        restoredCount: restoredCount,
        parentId: item.parentId
      },
      message: `${itemType === 'file' ? 'File' : 'Folder'} restored successfully`
    });

  } catch (error) {
    global.logger.logInfo(['trash/restore error', error.message], __dirname);
    console.error('Restore error:', error);

    return res.status(500).json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });
  }

  // Helper function to recursively restore folder and its contents
  async function restoreFolder(folderId, userId) {
    const FileModel = require('../../../../models/file');
    const FolderModel = require('../../../../models/folder');

    let totalRestored = 0;
    let totalStorageRestored = '0';
    let totalFilesRestored = 0;
    let totalFoldersRestored = 0;

    // Get all deleted subfolders
    const subfolders = await FolderModel.find({
      parentId: folderId,
      ownerId: userId,
      isDeleted: true,
      permanentlyDeletedAt: { $exists: false }
    }).select('_id').lean();

    // Recursively restore subfolders
    for (const subfolder of subfolders) {
      const subResult = await restoreFolder(subfolder._id, userId);
      totalRestored += subResult.totalRestored;
      totalStorageRestored = storageService.addStringNumbers(totalStorageRestored, subResult.totalStorageRestored);
      totalFilesRestored += subResult.totalFilesRestored;
      totalFoldersRestored += subResult.totalFoldersRestored;
    }

    // Get all deleted files in this folder to calculate storage restored
    const filesToRestore = await FileModel.find({
      parentId: folderId,
      ownerId: userId,
      isDeleted: true,
      permanentlyDeletedAt: { $exists: false }
    }).select('fileSize').lean();

    // Calculate total storage to be restored
    for (const file of filesToRestore) {
      if (file.fileSize) {
        totalStorageRestored = storageService.addStringNumbers(totalStorageRestored, file.fileSize.toString());
      }
      totalFilesRestored += 1;
    }

    // Restore all files in this folder
    const fileRestoreResult = await FileModel.updateMany(
      { 
        parentId: folderId, 
        ownerId: userId,
        isDeleted: true,
        permanentlyDeletedAt: { $exists: false }
      },
      {
        $unset: { deletedAt: 1 },
        $set: { isDeleted: false }
      }
    );
    totalRestored += fileRestoreResult.modifiedCount;

    // Restore all subfolders in this folder
    const subfolderRestoreResult = await FolderModel.updateMany(
      { 
        parentId: folderId, 
        ownerId: userId,
        isDeleted: true,
        permanentlyDeletedAt: { $exists: false }
      },
      {
        $unset: { deletedAt: 1 },
        $set: { isDeleted: false }
      }
    );
    totalRestored += subfolderRestoreResult.modifiedCount;
    totalFoldersRestored += subfolderRestoreResult.modifiedCount;

    // Restore the folder itself
    const mainFolderResult = await FolderModel.updateOne(
      { 
        _id: folderId, 
        ownerId: userId,
        isDeleted: true,
        permanentlyDeletedAt: { $exists: false }
      },
      {
        $unset: { deletedAt: 1 },
        $set: { isDeleted: false }
      }
    );
    totalRestored += mainFolderResult.modifiedCount;
    totalFoldersRestored += mainFolderResult.modifiedCount;

    // Update user storage usage if we have restored storage
    if (storageService.compareStringNumbers(totalStorageRestored, '0') > 0 || totalFoldersRestored > 0) {
      try {
        // Update storage and file count
        if (storageService.compareStringNumbers(totalStorageRestored, '0') > 0) {
          await storageService.addStorageUsageBulk(userId, totalStorageRestored, totalFilesRestored);
        }
        // Update folder count separately if needed
        if (totalFoldersRestored > 0) {
          await storageService.addFolderCount(userId, totalFoldersRestored);
        }
      } catch (storageError) {
        console.warn('Failed to update storage usage on folder restore:', storageError.message);
      }
    }

    return {
      totalRestored,
      totalStorageRestored,
      totalFilesRestored,
      totalFoldersRestored
    };
  }
};
