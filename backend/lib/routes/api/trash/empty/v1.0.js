const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const cacheService = require('../../../../services/cache');

module.exports = async (req, res) => {
  try {
    const userId = req.user ? req.user.id : null;

    if (!userId) {
      return res.status(401).json({
        code: CONSTANTS.CODE.UNAUTHORIZED,
        message: 'Authentication required'
      });
    }

    const FileModel = require('../../../../models/file');
    const FolderModel = require('../../../../models/folder');

    // Permanently delete all files in trash
    const fileDeleteResult = await FileModel.updateMany(
      { 
        ownerId: userId,
        isDeleted: true,
        permanentlyDeletedAt: { $exists: false }
      },
      {
        $set: { permanentlyDeletedAt: new Date() }
      }
    );

    // Permanently delete all folders in trash
    const folderDeleteResult = await FolderModel.updateMany(
      { 
        ownerId: userId,
        isDeleted: true,
        permanentlyDeletedAt: { $exists: false }
      },
      {
        $set: { permanentlyDeletedAt: new Date() }
      }
    );

    const totalDeleted = fileDeleteResult.modifiedCount + folderDeleteResult.modifiedCount;

    // Clear cache
    try {
      await cacheService.clearSearchCache();
    } catch (cacheError) {
      console.warn('Cache clear failed:', cacheError.message);
    }

    global.logger.logInfo(`Trash emptied for user ${userId}: ${totalDeleted} items permanently deleted`);

    return res.json({
      code: CONSTANTS.CODE.SUCCESS,
      data: {
        deletedCount: totalDeleted,
        filesDeleted: fileDeleteResult.modifiedCount,
        foldersDeleted: folderDeleteResult.modifiedCount
      },
      message: 'Trash emptied successfully'
    });

  } catch (error) {
    global.logger.logInfo(['trash/empty error', error.message], __dirname);
    console.error('Empty trash error:', error);

    return res.status(500).json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });
  }
};
