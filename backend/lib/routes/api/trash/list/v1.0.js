const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

module.exports = async (req, res) => {
  try {
    const userId = req.user ? req.user.id : null;

    if (!userId) {
      return res.status(401).json({
        code: CONSTANTS.CODE.UNAUTHORIZED,
        message: 'Authentication required'
      });
    }

    const FileModel = require('../../../../models/file');
    const FolderModel = require('../../../../models/folder');

    // Get deleted files
    const deletedFiles = await FileModel.find({
      ownerId: userId,
      isDeleted: true,
      permanentlyDeletedAt: { $exists: false } // Not permanently deleted
    })
    .select('_id originalFileName fileSize mimeType deletedAt parentId telegramFileId')
    .sort({ deletedAt: -1 })
    .lean();

    // Get deleted folders
    const deletedFolders = await FolderModel.find({
      ownerId: userId,
      isDeleted: true,
      permanentlyDeletedAt: { $exists: false } // Not permanently deleted
    })
    .select('_id folderName deletedAt parentId')
    .sort({ deletedAt: -1 })
    .lean();

    // Format files for response
    const formattedFiles = deletedFiles.map(file => ({
      id: file._id,
      name: file.originalFileName,
      type: 'file',
      size: file.fileSize,
      mimeType: file.mimeType,
      deletedAt: file.deletedAt,
      parentId: file.parentId,
      telegramFileId: file.telegramFileId
    }));

    // Format folders for response
    const formattedFolders = deletedFolders.map(folder => ({
      id: folder._id,
      name: folder.folderName,
      type: 'folder',
      deletedAt: folder.deletedAt,
      parentId: folder.parentId
    }));

    // Combine and sort by deletion date
    const allItems = [...formattedFiles, ...formattedFolders]
      .sort((a, b) => new Date(b.deletedAt).getTime() - new Date(a.deletedAt).getTime());

    // Calculate total size of deleted files
    const totalDeletedSize = deletedFiles.reduce((total, file) => total + (file.fileSize || 0), 0);

    global.logger.logInfo(`Trash listed for user ${userId}: ${allItems.length} items, ${totalDeletedSize} bytes`);

    return res.json({
      code: CONSTANTS.CODE.SUCCESS,
      data: {
        items: allItems,
        summary: {
          totalItems: allItems.length,
          totalFiles: formattedFiles.length,
          totalFolders: formattedFolders.length,
          totalSize: totalDeletedSize
        }
      },
      message: 'Trash items retrieved successfully'
    });

  } catch (error) {
    global.logger.logInfo(['trash/list error', error.message], __dirname);
    console.error('Trash list error:', error);

    return res.status(500).json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });
  }
};
