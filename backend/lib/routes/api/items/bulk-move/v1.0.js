const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const cacheService = require('../../../../services/cache');

module.exports = async (req, res) => {
  try {
    const { itemIds, destinationFolderId } = req.body;
    const userId = req.user ? req.user.id : null;

    if (!itemIds || !Array.isArray(itemIds) || itemIds.length === 0) {
      return res.status(400).json({
        code: CONSTANTS.CODE.INVALID_PARAMS,
        message: 'Item IDs array is required and cannot be empty'
      });
    }

    // destinationFolderId can be null for root folder
    if (destinationFolderId !== null && typeof destinationFolderId !== 'string') {
      return res.status(400).json({
        code: CONSTANTS.CODE.INVALID_PARAMS,
        message: 'Invalid destinationFolderId format'
      });
    }

    const FileModel = require('../../../../models/file');
    const FolderModel = require('../../../../models/folder');

    // Validate destination folder exists and user has access (if not null)
    if (destinationFolderId !== null) {
      const destinationFolder = await FolderModel.findOne({
        _id: destinationFolderId,
        isDeleted: false,
        ownerId: userId
      });

      if (!destinationFolder) {
        return res.status(404).json({
          code: CONSTANTS.CODE.NOT_FOUND,
          message: 'Destination folder not found or access denied'
        });
      }
    }

    const results = [];
    const errors = [];
    const affectedFolders = new Set();

    // Process each item
    for (const itemId of itemIds) {
      try {
        // Try to find as file first
        let item = await FileModel.findOne({
          _id: itemId,
          isDeleted: false,
          ownerId: userId
        });

        let itemType = 'file';
        let updateResult;
        let oldParentId = null;

        if (item) {
          oldParentId = item.parentId;
          affectedFolders.add(oldParentId);
          affectedFolders.add(destinationFolderId);

          // It's a file - update parentId
          updateResult = await FileModel.updateOne(
            { _id: itemId, isDeleted: false, ownerId: userId },
            { parentId: destinationFolderId }
          );
        } else {
          // Try to find as folder
          item = await FolderModel.findOne({
            _id: itemId,
            isDeleted: false,
            ownerId: userId
          });

          if (!item) {
            errors.push({
              itemId,
              error: 'Item not found or access denied'
            });
            continue;
          }

          itemType = 'folder';
          oldParentId = item.parentId;
          affectedFolders.add(oldParentId);
          affectedFolders.add(destinationFolderId);

          // Không cho phép di chuyển folder vào chính nó
          if (itemId === destinationFolderId) {
            errors.push({
              itemId,
              error: 'Cannot move a folder into itself'
            });
            continue;
          }

          // Kiểm tra folder đích có phải là con của folder hiện tại không (chỉ khi destinationFolderId không null)
          if (destinationFolderId !== null) {
            let current = await FolderModel.findOne({
              _id: destinationFolderId,
              isDeleted: false,
              ownerId: userId
            });

            let isSubfolder = false;
            while (current) {
              if (current.parentId && current.parentId.toString() === itemId) {
                isSubfolder = true;
                break;
              }
              if (!current.parentId) break;
              current = await FolderModel.findOne({
                _id: current.parentId,
                isDeleted: false,
                ownerId: userId
              });
            }

            if (isSubfolder) {
              errors.push({
                itemId,
                error: 'Cannot move a folder into its subfolder'
              });
              continue;
            }
          }

          // Update parentId
          updateResult = await FolderModel.updateOne(
            { _id: itemId, isDeleted: false, ownerId: userId },
            { parentId: destinationFolderId }
          );
        }

        if (updateResult.matchedCount === 0) {
          errors.push({
            itemId,
            error: 'Item not found or already moved'
          });
        } else {
          results.push({
            id: itemId,
            type: itemType,
            oldParentId,
            newParentId: destinationFolderId
          });
        }

      } catch (itemError) {
        console.error(`Error moving item ${itemId}:`, itemError);
        errors.push({
          itemId,
          error: itemError.message || 'Unknown error occurred'
        });
      }
    }

    // Clear cache for all affected folders
    try {
      for (const folderId of affectedFolders) {
        await cacheService.clearFolderCache(folderId);
      }
      await cacheService.clearSearchCache();
    } catch (cacheError) {
      console.warn('Cache clear failed:', cacheError.message);
    }

    const successCount = results.length;
    const errorCount = errors.length;

    global.logger.logInfo(`Bulk move completed: ${successCount} successful, ${errorCount} errors`);

    return res.json({
      code: CONSTANTS.CODE.SUCCESS,
      data: {
        successful: results,
        errors: errors,
        summary: {
          total: itemIds.length,
          successful: successCount,
          failed: errorCount
        }
      },
      message: `Bulk move completed: ${successCount} items moved successfully${errorCount > 0 ? `, ${errorCount} failed` : ''}`
    });

  } catch (error) {
    global.logger.logInfo(['items/bulk-move error', error.message], __dirname);
    console.error('Bulk move error:', error);

    return res.status(500).json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });
  }
};
