const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const cacheService = require('../../../services/cache');
const activityLogger = require('../../../services/activityLogger');
const UserActivityLogModel = require('../../../models/userActivityLog');

module.exports = async (req, res) => {
  const startTime = Date.now();
  const userId = req.user ? req.user.id : null;
  let searchQuery = null;

  try {
    const query = req.query.q;

    // Validate query
    if (!query || typeof query !== 'string' || query.trim().length === 0) {
      // Log failed search attempt
      if (userId) {
        await activityLogger.logSearch(
          userId,
          UserActivityLogModel.ACTION_TYPES.SEARCH_QUERY,
          '',
          {
            status: UserActivityLogModel.STATUS_TYPES.FAILURE,
            errorCode: 'EMPTY_QUERY',
            errorMessage: 'Search query is required',
            requestMetadata: {
              method: req.method,
              url: req.originalUrl,
              userAgent: req.get('User-Agent'),
              ipAddress: req.ip || req.connection?.remoteAddress || '127.0.0.1',
              responseTime: Date.now() - startTime
            }
          }
        );
      }

      return res.status(400).json({
        code: CONSTANTS.CODE.INVALID_PARAMS,
        message: 'Search query is required'
      });
    }

    if (query.trim().length < 2) {
      // Log failed search attempt
      if (userId) {
        await activityLogger.logSearch(
          userId,
          UserActivityLogModel.ACTION_TYPES.SEARCH_QUERY,
          query.trim(),
          {
            status: UserActivityLogModel.STATUS_TYPES.FAILURE,
            errorCode: 'QUERY_TOO_SHORT',
            errorMessage: 'Search query must be at least 2 characters long',
            requestMetadata: {
              method: req.method,
              url: req.originalUrl,
              userAgent: req.get('User-Agent'),
              ipAddress: req.ip || req.connection?.remoteAddress || '127.0.0.1',
              responseTime: Date.now() - startTime
            }
          }
        );
      }

      return res.status(400).json({
        code: CONSTANTS.CODE.INVALID_PARAMS,
        message: 'Search query must be at least 2 characters long'
      });
    }

    searchQuery = query.trim();

    // Check cache first
    let cachedData = null;
    try {
      const cacheKey = cacheService.getSearchKey(searchQuery);
      cachedData = await cacheService.get(cacheKey);

      if (cachedData) {
        return res.json({
          code: CONSTANTS.CODE.SUCCESS,
          data: cachedData,
          message: 'Search completed successfully'
        });
      }
    } catch (error) {
      console.error('Cache error:', error);
      // Continue without cache
    }

    // Perform database search
    const FileModel = require('../../../models/file');
    const FolderModel = require('../../../models/folder');

    // Create case-insensitive regex for search
    const searchRegex = new RegExp(searchQuery.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'i');

    // Search files
    const files = await FileModel.find({
      originalFileName: searchRegex,
      isDeleted: false
    })
    .select('_id originalFileName fileSize mimeType uploadDate parentId telegramFileId')
    .sort({ originalFileName: 1 })
    .limit(100) // Limit results to prevent performance issues
    .lean();

    // Search folders
    const folders = await FolderModel.find({
      folderName: searchRegex,
      isDeleted: false
    })
    .select('_id folderName createdAt parentId')
    .sort({ folderName: 1 })
    .limit(100) // Limit results to prevent performance issues
    .lean();

    // Format response
    const result = {
      query: searchQuery,
      totalResults: files.length + folders.length,
      folders: folders.map(folder => ({
        id: folder._id,
        name: folder.folderName,
        type: 'folder',
        createdAt: folder.createdAt,
        parentId: folder.parentId
      })),
      files: files.map(file => ({
        id: file._id,
        name: file.originalFileName,
        type: 'file',
        size: file.fileSize,
        mimeType: file.mimeType,
        uploadDate: file.uploadDate,
        parentId: file.parentId,
        telegramFileId: file.telegramFileId
      }))
    };

    // Cache the result for 10 minutes
    try {
      const cacheKey = cacheService.getSearchKey(searchQuery);
      await cacheService.set(cacheKey, result, 600);
    } catch (error) {
      console.error('Cache set error:', error);
      // Continue without caching
    }

    // Log successful search
    if (userId) {
      await activityLogger.logSearch(
        userId,
        UserActivityLogModel.ACTION_TYPES.SEARCH_QUERY,
        searchQuery,
        {
          status: UserActivityLogModel.STATUS_TYPES.SUCCESS,
          resultCount: result.files.length + result.folders.length,
          requestMetadata: {
            method: req.method,
            url: req.originalUrl,
            userAgent: req.get('User-Agent'),
            ipAddress: req.ip || req.connection?.remoteAddress || '127.0.0.1',
            responseTime: Date.now() - startTime
          },
          additionalData: {
            filesFound: result.files.length,
            foldersFound: result.folders.length,
            cacheHit: !!cachedData
          }
        }
      );
    }

    res.json({
      code: CONSTANTS.CODE.SUCCESS,
      data: result,
      message: 'Search completed successfully'
    });

  } catch (error) {
    console.error('Search error:', error);

    // Log search error
    if (userId) {
      await activityLogger.logError(
        userId,
        UserActivityLogModel.ACTION_TYPES.SEARCH_QUERY,
        error,
        {
          searchQuery: searchQuery,
          requestMetadata: {
            method: req.method,
            url: req.originalUrl,
            userAgent: req.get('User-Agent'),
            ipAddress: req.ip || req.connection?.remoteAddress || '127.0.0.1',
            responseTime: Date.now() - startTime
          }
        }
      );
    }

    res.status(500).json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });
  }
};
