const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const cacheService = require('../../../../services/cache');
const storageService = require('../../../../services/storageService');
const config = require('../../../../config');

module.exports = async (req, res) => {
  try {
    const { folderName, parentId } = req.body;
    const userId = req.user ? req.user.id : null;

    console.log('=== FOLDER CREATE API CALLED ===');
    console.log('Request body:', req.body);
    console.log('Extracted folderName:', folderName, 'parentId:', parentId);

    // Step 1: Validate input
    console.log('Step 1: Validating input...');
    if (!folderName || typeof folderName !== 'string' || folderName.trim().length === 0) {
      return res.status(400).json({
        code: CONSTANTS.CODE.INVALID_PARAMS,
        message: 'Folder name is required and must be a non-empty string'
      });
    }

    // Step 2: Check for invalid characters
    console.log('Step 2: Checking for invalid characters...');
    const invalidChars = /[<>:"/\\|?*]/;
    if (invalidChars.test(folderName.trim())) {
      return res.status(400).json({
        code: CONSTANTS.CODE.INVALID_PARAMS,
        message: 'Folder name contains invalid characters'
      });
    }

    // Step 3: Use mock data if enabled
    if (config.has('mockData') && config.get('mockData')) {
      console.log('Using mock data service...');
      const mockDataService = require('../../../../services/mockData');

      try {
        const result = await mockDataService.createFolder(folderName.trim(), parentId);

        return res.json({
          code: CONSTANTS.CODE.SUCCESS,
          data: result,
          message: 'Folder created successfully'
        });
      } catch (error) {
        console.log('Mock folder create error:', error);
        return res.status(500).json({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: error.message || 'Failed to create folder'
        });
      }
    }

    // Step 3: Load models
    console.log('Step 3: Loading FolderModel...');
    const FolderModel = require('../../../../models/folder');
    console.log('FolderModel loaded successfully');

    // Step 4: Validate parent folder if provided
    if (parentId) {
      console.log('Validating parent folder:', parentId);
      const parentFolder = await FolderModel.findOne({
        _id: parentId,
        isDeleted: false,
        ownerId: userId // Ensure user can only create folders in their own folders
      }).lean();

      if (!parentFolder) {
        return res.status(400).json({
          code: CONSTANTS.CODE.INVALID_PARAMS,
          message: 'Parent folder not found or access denied'
        });
      }
      console.log('Parent folder validated successfully');
    }

    // Step 5: Check for existing folder
    console.log('Step 4: Checking for existing folder...');
    const queryParams = {
      folderName: folderName.trim(),
      parentId: parentId || null,
      isDeleted: false
    };
    console.log('Query params:', queryParams);

    const existingFolder = await FolderModel.findOne(queryParams).lean();
    console.log('Step 4 completed. Existing folder:', existingFolder);

    if (existingFolder) {
      return res.status(400).json({
        code: CONSTANTS.CODE.INVALID_PARAMS,
        message: 'A folder with this name already exists in the current location'
      });
    }

    // Step 5: Create new folder
    console.log('Step 5: Creating new folder...');
    const newFolder = new FolderModel({
      folderName: folderName.trim(),
      parentId: parentId || null,
      createdAt: new Date(),
      isDeleted: false,
      deletedAt: null,
      ownerId: userId
    });
    console.log('New folder object created:', newFolder);

    // Step 6: Save to database
    console.log('Step 6: Saving folder to database...');
    const savedFolder = await newFolder.save();
    console.log('Folder saved successfully:', savedFolder);

    // Step 6.5: Update user folder count
    if (userId) {
      try {
        await storageService.addFolderCount(userId);
        console.log('User folder count updated successfully');
      } catch (storageError) {
        console.warn('Failed to update folder count:', storageError.message);
      }
    }

    // Step 7: Send response
    console.log('Step 7: Sending response...');
    const responseObject = {
      code: CONSTANTS.CODE.SUCCESS,
      data: {
        id: savedFolder._id,
        folderName: savedFolder.folderName,
        parentId: savedFolder.parentId,
        createdAt: savedFolder.createdAt
      },
      message: 'Folder created successfully'
    };
    console.log('Response object:', responseObject);

    res.json(responseObject);
    console.log('Response sent successfully');

    // Step 8: Clear cache asynchronously
    try {
      await cacheService.clearFolderCache(parentId || null);
      await cacheService.clearSearchCache();
    } catch (cacheError) {
      console.warn('Cache clear failed:', cacheError.message);
    }

    global.logger.logInfo(`Folder created successfully: ${folderName.trim()}, ID: ${savedFolder._id}`);
    console.log('=== FOLDER CREATE API FINISHED ===');

  } catch (error) {
    global.logger.logInfo(['folders/create error', error.message], __dirname);
    console.error('Folder create error:', error);

    // Handle duplicate key error (in case of race condition)
    if (error.code === 11000) {
      return res.status(400).json({
        code: CONSTANTS.CODE.INVALID_PARAMS,
        message: 'A folder with this name already exists in the current location'
      });
    } else {
      return res.status(500).json({
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      });
    }
  }
};
