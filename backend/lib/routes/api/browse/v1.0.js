const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const cacheService = require('../../../services/cache');
const activityLogger = require('../../../services/activityLogger');
const UserActivityLogModel = require('../../../models/userActivityLog');
const async = require('async');
const _ = require('lodash');
const config = require('../../../config');

module.exports = async (req, res) => {
  const startTime = Date.now();
  const userId = req.user ? req.user.id : null;
  const folderId = req.params.folderId || null;

  // Log referer for debugging
  const referer = req.headers.referer;
  if (referer && referer.includes('/files/preview/')) {
    console.log('⚠️  Request from iframe preview detected (but allowing):', referer);
  }

  const validateParams = (next) => {
    try {
      const folderId = req.params.folderId || null;
      const userId = req.user ? req.user.id : null;

      // Validate folder exists if folderId is provided
      if (folderId) {
        const FolderModel = require('../../../models/folder');
        FolderModel.findOne({
          _id: folderId,
          isDeleted: false,
          ownerId: userId // Ensure user can only browse their own folders
        }).lean()
        .then(folder => {
          if (!folder) {
            return next({
              code: CONSTANTS.CODE.NOT_FOUND,
              message: 'Folder not found or access denied'
            });
          }
          next(null, folderId);
        })
        .catch(error => {
          global.logger.logError(['browse/validateParams', error], __dirname);
          next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          });
        });
      } else {
        next(null, folderId);
      }
    } catch (error) {
      global.logger.logError(['browse/validateParams', error], __dirname);
      next({
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      });
    }
  };

  const checkCache = (folderId, next) => {
    // Temporarily disable cache
    next(null, folderId, null);
  };

  const getFolderContent = (folderId, cachedData, next) => {
    try {
      const userId = req.user ? req.user.id : null;

      if (cachedData) {
        return next(null, cachedData);
      }

      // Use mock data if enabled
      if (config.has('mockData') && config.get('mockData')) {
        const mockDataService = require('../../../services/mockData');
        mockDataService.getFolderContent(folderId)
          .then(result => {
            next(null, result);
          })
          .catch(error => {
            global.logger.logError(['browse/getFolderContent-mock', error], __dirname);
            next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: MESSAGES.SYSTEM.ERROR
            });
          });
        return;
      }

      const FileModel = require('../../../models/file');
      const FolderModel = require('../../../models/folder');

      // Get folders and files in parallel (only user's own content)
      Promise.all([
        FolderModel.find({
          parentId: folderId,
          isDeleted: false,
          ownerId: userId
        })
        .select('_id folderName createdAt parentId')
        .sort({ folderName: 1 })
        .lean(),

        FileModel.find({
          parentId: folderId,
          isDeleted: false,
          ownerId: userId
        })
        .select('_id originalFileName fileSize mimeType uploadDate parentId telegramFileId fileMetadata')
        .sort({ originalFileName: 1 })
        .lean()
      ])
      .then(([folders, files]) => {
        // Format response
        const result = {
          folders: folders.map(folder => ({
            id: folder._id,
            name: folder.folderName,
            type: 'folder',
            createdAt: folder.createdAt,
            parentId: folder.parentId
          })),
          files: files.map(file => ({
            id: file._id,
            name: file.originalFileName,
            type: 'file',
            size: file.fileSize,
            mimeType: file.mimeType,
            uploadDate: file.uploadDate,
            parentId: file.parentId,
            telegramFileId: file.telegramFileId,
            hasThumbnail: !!(file.fileMetadata && file.fileMetadata.thumbnail),
            thumbnailId: file.fileMetadata?.thumbnail || null,
            dimensions: file.fileMetadata ? {
              width: file.fileMetadata.width,
              height: file.fileMetadata.height
            } : null
          }))
        };

        // Cache the result
        const cacheKey = cacheService.getFolderContentKey(folderId);
        cacheService.set(cacheKey, result, 900) // 15 minutes
          .then(() => {
            next(null, result);
          })
          .catch(() => {
            // Continue even if caching fails
            next(null, result);
          });
      })
      .catch(error => {
        global.logger.logError(['browse/getFolderContent', error], __dirname);
        next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGES.SYSTEM.ERROR
        });
      });
    } catch (error) {
      global.logger.logError(['browse/getFolderContent', error], __dirname);
      next({
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      });
    }
  };

  async.waterfall([validateParams, checkCache, getFolderContent], async (err, data) => {
    if (err && _.isError(err)) {
      // Log navigation error
      if (userId) {
        await activityLogger.logError(
          userId,
          UserActivityLogModel.ACTION_TYPES.FOLDER_BROWSE,
          err,
          {
            folderId: folderId,
            requestMetadata: {
              method: req.method,
              url: req.originalUrl,
              userAgent: req.get('User-Agent'),
              ipAddress: req.ip || req.connection?.remoteAddress || '127.0.0.1',
              responseTime: Date.now() - startTime
            }
          }
        );
      }

      data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      };
    } else {
      // Log successful navigation
      if (userId) {
        await activityLogger.logNavigation(
          userId,
          UserActivityLogModel.ACTION_TYPES.FOLDER_BROWSE,
          folderId,
          {
            status: UserActivityLogModel.STATUS_TYPES.SUCCESS,
            itemCount: (data?.files?.length || 0) + (data?.folders?.length || 0),
            requestMetadata: {
              method: req.method,
              url: req.originalUrl,
              userAgent: req.get('User-Agent'),
              ipAddress: req.ip || req.connection?.remoteAddress || '127.0.0.1',
              responseTime: Date.now() - startTime
            },
            additionalData: {
              filesCount: data?.files?.length || 0,
              foldersCount: data?.folders?.length || 0,
              cacheHit: data?.fromCache || false
            }
          }
        );
      }
    }

    const response = err || {
      code: CONSTANTS.CODE.SUCCESS,
      data: data,
      message: 'Folder content retrieved successfully'
    };

    res.json(response);
  });
};
