const UserActivityLogModel = require('../models/userActivityLog');
const UAParser = require('ua-parser-js');
const geoip = require('geoip-lite');
const _ = require('lodash');

class ActivityLoggerService {
  constructor() {
    this.queue = [];
    this.isProcessing = false;
    this.batchSize = 10;
    this.flushInterval = 5000; // 5 seconds
    
    // Start batch processing
    this.startBatchProcessor();
  }

  /**
   * Log user activity asynchronously
   * @param {Object} logData - Activity log data
   * @returns {Promise<void>}
   */
  async logActivity(logData) {
    try {
      // Validate required fields
      if (!logData.userId || !logData.action) {
        global.logger.logError('ActivityLogger: Missing required fields (userId, action)');
        return;
      }

      // Enrich log data with additional context
      const enrichedData = await this.enrichLogData(logData);
      
      // Add to queue for batch processing
      this.queue.push(enrichedData);
      
      // If queue is full, process immediately
      if (this.queue.length >= this.batchSize) {
        await this.processBatch();
      }
    } catch (error) {
      global.logger.logError('ActivityLogger: Error logging activity:', error.message);
    }
  }

  /**
   * Enrich log data with additional context
   * @param {Object} logData - Raw log data
   * @returns {Object} - Enriched log data
   */
  async enrichLogData(logData) {
    const enriched = { ...logData };
    
    // Parse user agent if available
    if (logData.requestMetadata?.userAgent) {
      const parser = new UAParser(logData.requestMetadata.userAgent);
      const result = parser.getResult();
      
      enriched.sessionInfo = {
        ...enriched.sessionInfo,
        deviceType: result.device.type || 'desktop',
        browserName: result.browser.name,
        browserVersion: result.browser.version,
        osName: result.os.name,
        osVersion: result.os.version
      };
    }
    
    // Add geolocation data if IP address is available
    if (logData.requestMetadata?.ipAddress && logData.requestMetadata.ipAddress !== '127.0.0.1') {
      const geo = geoip.lookup(logData.requestMetadata.ipAddress);
      if (geo) {
        enriched.requestMetadata.geolocation = {
          ...enriched.requestMetadata.geolocation,
          city: geo.city,
          country: geo.country,
          latitude: geo.ll?.[0],
          longitude: geo.ll?.[1]
        };
      }
    }
    
    // Set default timestamp if not provided
    if (!enriched.timestamp) {
      enriched.timestamp = new Date();
    }
    
    // Set default status if not provided
    if (!enriched.status) {
      enriched.status = UserActivityLogModel.STATUS_TYPES.SUCCESS;
    }
    
    // Add tags based on action type
    enriched.tags = this.generateTags(enriched);
    
    // Set category based on action
    enriched.category = this.determineCategory(enriched.action);
    
    return enriched;
  }

  /**
   * Generate tags for the activity
   * @param {Object} logData - Log data
   * @returns {Array} - Array of tags
   */
  generateTags(logData) {
    const tags = [];
    
    // Add action-based tags
    if (logData.action.includes('file')) {
      tags.push('file-operation');
    }
    if (logData.action.includes('auth') || logData.action.includes('login') || logData.action.includes('register')) {
      tags.push('authentication');
    }
    if (logData.action.includes('search')) {
      tags.push('search');
    }
    if (logData.action.includes('admin')) {
      tags.push('admin');
    }
    
    // Add status-based tags
    if (logData.status === UserActivityLogModel.STATUS_TYPES.ERROR) {
      tags.push('error');
    }
    if (logData.status === UserActivityLogModel.STATUS_TYPES.FAILURE) {
      tags.push('failure');
    }
    
    // Add resource-based tags
    if (logData.resourceType) {
      tags.push(`resource-${logData.resourceType}`);
    }
    
    return tags;
  }

  /**
   * Determine category based on action
   * @param {String} action - Action type
   * @returns {String} - Category
   */
  determineCategory(action) {
    if (action.includes('login') || action.includes('register') || action.includes('password')) {
      return 'security';
    }
    if (action.includes('admin')) {
      return 'system';
    }
    if (action.includes('error') || action.includes('failure')) {
      return 'error';
    }
    return 'user_behavior';
  }

  /**
   * Start batch processor
   */
  startBatchProcessor() {
    setInterval(async () => {
      if (this.queue.length > 0) {
        await this.processBatch();
      }
    }, this.flushInterval);
  }

  /**
   * Process batch of log entries
   */
  async processBatch() {
    if (this.isProcessing || this.queue.length === 0) {
      return;
    }

    this.isProcessing = true;
    const batch = this.queue.splice(0, this.batchSize);

    try {
      await UserActivityLogModel.insertMany(batch, { ordered: false });
      global.logger.logInfo(`ActivityLogger: Successfully logged ${batch.length} activities`);
    } catch (error) {
      global.logger.logError('ActivityLogger: Error saving batch:', error.message);
      
      // Try to save individually to identify problematic entries
      for (const entry of batch) {
        try {
          await new UserActivityLogModel(entry).save();
        } catch (individualError) {
          global.logger.logError('ActivityLogger: Error saving individual entry:', individualError.message);
        }
      }
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Flush all pending logs immediately
   */
  async flush() {
    while (this.queue.length > 0) {
      await this.processBatch();
    }
  }

  // Convenience methods for common activities

  /**
   * Log authentication activity
   */
  async logAuth(userId, action, status, metadata = {}) {
    return this.logActivity({
      userId,
      action,
      status,
      resourceType: 'auth',
      category: 'security',
      additionalData: {
        authProvider: metadata.provider || 'local',
        loginMethod: metadata.method || 'password',
        ...metadata
      }
    });
  }

  /**
   * Log file operation
   */
  async logFileOperation(userId, action, fileId, fileName, metadata = {}) {
    return this.logActivity({
      userId,
      action,
      resourceType: 'file',
      resourceId: fileId,
      resourceName: fileName,
      additionalData: {
        fileSize: metadata.fileSize,
        mimeType: metadata.mimeType,
        fileExtension: metadata.fileExtension,
        ...metadata
      }
    });
  }

  /**
   * Log search activity
   */
  async logSearch(userId, query, filters = {}, resultsCount = 0, metadata = {}) {
    return this.logActivity({
      userId,
      action: UserActivityLogModel.ACTION_TYPES.SEARCH_QUERY,
      resourceType: 'search',
      additionalData: {
        searchQuery: query,
        searchFilters: filters,
        searchResultsCount: resultsCount,
        ...metadata
      }
    });
  }

  /**
   * Log navigation activity
   */
  async logNavigation(userId, path, folderId = null, metadata = {}) {
    return this.logActivity({
      userId,
      action: UserActivityLogModel.ACTION_TYPES.FOLDER_NAVIGATE,
      resourceType: 'folder',
      resourceId: folderId,
      resourcePath: path,
      additionalData: metadata
    });
  }

  /**
   * Log error activity
   */
  async logError(userId, action, error, metadata = {}) {
    return this.logActivity({
      userId,
      action,
      status: UserActivityLogModel.STATUS_TYPES.ERROR,
      category: 'error',
      additionalData: {
        errorCode: error.code || 'UNKNOWN',
        errorMessage: error.message,
        errorStack: error.stack,
        ...metadata
      }
    });
  }
}

// Create singleton instance
const activityLogger = new ActivityLoggerService();

// Graceful shutdown - flush pending logs
process.on('SIGTERM', async () => {
  await activityLogger.flush();
});

process.on('SIGINT', async () => {
  await activityLogger.flush();
});

module.exports = activityLogger;
