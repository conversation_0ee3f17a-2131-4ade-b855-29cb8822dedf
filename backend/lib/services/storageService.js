const UserModel = require('../models/user');
const FileModel = require('../models/file');
const FolderModel = require('../models/folder');
const mongoose = require('mongoose');

class StorageService {
  /**
   * Add two string numbers safely
   * @param {string} a - First number as string
   * @param {string} b - Second number as string
   * @returns {string} - Sum as string
   */
  addStringNumbers(a, b) {
    return (Number(a) + Number(b)).toString();
  }

  /**
   * Subtract two string numbers safely
   * @param {string} a - First number as string
   * @param {string} b - Second number as string
   * @returns {string} - Difference as string
   */
  subtractStringNumbers(a, b) {
    const result = Number(a) - Number(b);
    return result < 0 ? '0' : result.toString();
  }

  /**
   * Compare two string numbers
   * @param {string} a - First number as string
   * @param {string} b - Second number as string
   * @returns {number} - -1 if a < b, 0 if a === b, 1 if a > b
   */
  compareStringNumbers(a, b) {
    if (Number(a) < Number(b)) return -1;
    if (Number(a) > Number(b)) return 1;
    return 0;
  }

  /**
   * Calculate total storage used by a user
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Total storage used in bytes and file count
   */
  async calculateUserStorageUsed(userId) {
    try {
      const result = await FileModel.aggregate([
        {
          $match: {
            ownerId: new mongoose.Types.ObjectId(userId),
            isDeleted: false
          }
        },
        {
          $group: {
            _id: null,
            totalSize: { $sum: '$fileSize' },
            fileCount: { $sum: 1 }
          }
        }
      ]);

      return {
        totalSize: result.length > 0 ? result[0].totalSize.toString() : '0',
        fileCount: result.length > 0 ? result[0].fileCount : 0
      };
    } catch (error) {
      console.error('Error calculating user storage:', error);
      throw error;
    }
  }

  /**
   * Calculate total folders created by a user
   * @param {string} userId - User ID
   * @returns {Promise<number>} - Total folder count
   */
  async calculateUserFolderCount(userId) {
    try {
      const folderCount = await FolderModel.countDocuments({
        ownerId: new mongoose.Types.ObjectId(userId),
        isDeleted: false
      });

      return folderCount;
    } catch (error) {
      console.error('Error calculating user folder count:', error);
      throw error;
    }
  }

  /**
   * Update user storage statistics
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated storage info
   */
  async updateUserStorageStats(userId) {
    try {
      const storageData = await this.calculateUserStorageUsed(userId);
      const folderCount = await this.calculateUserFolderCount(userId);

      const updatedUser = await UserModel.findByIdAndUpdate(
        userId,
        {
          $set: {
            storageUsed: storageData.totalSize, // Already a string
            fileCount: storageData.fileCount,
            folderCount: folderCount,
            updatedAt: Date.now()
          }
        },
        { new: true }
      ).select('storageUsed storageQuota fileCount folderCount');

      return updatedUser;
    } catch (error) {
      console.error('Error updating user storage stats:', error);
      throw error;
    }
  }

  /**
   * Check if user has enough storage quota for a new file
   * @param {string} userId - User ID
   * @param {number} fileSize - Size of the new file in bytes
   * @returns {Promise<Object>} - Quota check result
   */
  async checkStorageQuota(userId, fileSize) {
    try {
      const user = await UserModel.findById(userId).select('storageUsed storageQuota');

      if (!user) {
        throw new Error('User not found');
      }

      const fileSizeStr = fileSize.toString();
      const availableSpace = this.subtractStringNumbers(user.storageQuota, user.storageUsed);
      const hasEnoughSpace = this.compareStringNumbers(availableSpace, fileSizeStr) >= 0;

      return {
        hasEnoughSpace,
        availableSpace,
        storageUsed: user.storageUsed,
        storageQuota: user.storageQuota,
        fileSize: fileSizeStr,
        wouldExceedBy: hasEnoughSpace ? '0' : this.subtractStringNumbers(fileSizeStr, availableSpace)
      };
    } catch (error) {
      console.error('Error checking storage quota:', error);
      throw error;
    }
  }

  /**
   * Add storage usage when a file is uploaded
   * @param {string} userId - User ID
   * @param {number} fileSize - Size of the uploaded file in bytes
   * @returns {Promise<Object>} - Updated storage info
   */
  async addStorageUsage(userId, fileSize) {
    try {
      const user = await UserModel.findById(userId).select('storageUsed fileCount');
      if (!user) {
        throw new Error('User not found');
      }

      const newStorageUsed = this.addStringNumbers(user.storageUsed, fileSize.toString());

      const updatedUser = await UserModel.findByIdAndUpdate(
        userId,
        {
          $set: {
            storageUsed: newStorageUsed,
            updatedAt: Date.now()
          },
          $inc: {
            fileCount: 1
          }
        },
        { new: true }
      ).select('storageUsed storageQuota fileCount folderCount');

      return updatedUser;
    } catch (error) {
      console.error('Error adding storage usage:', error);
      throw error;
    }
  }

  /**
   * Remove storage usage when a file is deleted
   * @param {string} userId - User ID
   * @param {number|string} fileSize - Size of the deleted file in bytes
   * @returns {Promise<Object>} - Updated storage info
   */
  async removeStorageUsage(userId, fileSize) {
    try {
      const user = await UserModel.findById(userId).select('storageUsed fileCount');
      if (!user) {
        throw new Error('User not found');
      }

      const newStorageUsed = this.subtractStringNumbers(user.storageUsed, fileSize.toString());

      const updatedUser = await UserModel.findByIdAndUpdate(
        userId,
        {
          $set: {
            storageUsed: newStorageUsed,
            updatedAt: Date.now()
          },
          $inc: {
            fileCount: -1
          }
        },
        { new: true }
      ).select('storageUsed storageQuota fileCount folderCount');

      return updatedUser;
    } catch (error) {
      console.error('Error removing storage usage:', error);
      throw error;
    }
  }

  /**
   * Remove storage usage for multiple files (bulk operation)
   * @param {string} userId - User ID
   * @param {string} totalStorageToRemove - Total storage to remove as string
   * @param {number} fileCount - Number of files being removed
   * @returns {Promise<Object>} - Updated storage info
   */
  async removeStorageUsageBulk(userId, totalStorageToRemove, fileCount = 0) {
    try {
      const user = await UserModel.findById(userId).select('storageUsed fileCount');
      if (!user) {
        throw new Error('User not found');
      }

      const newStorageUsed = this.subtractStringNumbers(user.storageUsed, totalStorageToRemove);

      const updatedUser = await UserModel.findByIdAndUpdate(
        userId,
        {
          $set: {
            storageUsed: newStorageUsed,
            updatedAt: Date.now()
          },
          $inc: {
            fileCount: -fileCount
          }
        },
        { new: true }
      ).select('storageUsed storageQuota fileCount folderCount');

      return updatedUser;
    } catch (error) {
      console.error('Error removing bulk storage usage:', error);
      throw error;
    }
  }

  /**
   * Add storage usage for multiple files (bulk operation)
   * @param {string} userId - User ID
   * @param {string} totalStorageToAdd - Total storage to add as string
   * @param {number} fileCount - Number of files being added
   * @returns {Promise<Object>} - Updated storage info
   */
  async addStorageUsageBulk(userId, totalStorageToAdd, fileCount = 0) {
    try {
      const user = await UserModel.findById(userId).select('storageUsed fileCount');
      if (!user) {
        throw new Error('User not found');
      }

      const newStorageUsed = this.addStringNumbers(user.storageUsed, totalStorageToAdd);

      const updatedUser = await UserModel.findByIdAndUpdate(
        userId,
        {
          $set: {
            storageUsed: newStorageUsed,
            updatedAt: Date.now()
          },
          $inc: {
            fileCount: fileCount
          }
        },
        { new: true }
      ).select('storageUsed storageQuota fileCount folderCount');

      return updatedUser;
    } catch (error) {
      console.error('Error adding bulk storage usage:', error);
      throw error;
    }
  }

  /**
   * Add folder count when a folder is created
   * @param {string} userId - User ID
   * @param {number} folderCount - Number of folders added (for recursive restoration)
   * @returns {Promise<Object>} - Updated storage info
   */
  async addFolderCount(userId, folderCount = 1) {
    try {
      const updatedUser = await UserModel.findByIdAndUpdate(
        userId,
        {
          $inc: { folderCount: folderCount },
          $set: { updatedAt: Date.now() }
        },
        { new: true }
      ).select('storageUsed storageQuota fileCount folderCount');

      return updatedUser;
    } catch (error) {
      console.error('Error adding folder count:', error);
      throw error;
    }
  }

  /**
   * Remove folder count when a folder is deleted
   * @param {string} userId - User ID
   * @param {number} folderCount - Number of folders deleted (for recursive deletion)
   * @returns {Promise<Object>} - Updated storage info
   */
  async removeFolderCount(userId, folderCount = 1) {
    try {
      const updatedUser = await UserModel.findByIdAndUpdate(
        userId,
        {
          $inc: { folderCount: -folderCount },
          $set: { updatedAt: Date.now() }
        },
        { new: true }
      ).select('storageUsed storageQuota fileCount folderCount');

      // Ensure folder count doesn't go below 0
      if (updatedUser.folderCount < 0) {
        await UserModel.findByIdAndUpdate(userId, {
          $set: { folderCount: 0 }
        });
        updatedUser.folderCount = 0;
      }

      return updatedUser;
    } catch (error) {
      console.error('Error removing folder count:', error);
      throw error;
    }
  }

  /**
   * Get user storage information
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Storage information
   */
  async getUserStorageInfo(userId) {
    try {
      const user = await UserModel.findById(userId)
        .select('storageUsed storageQuota fileCount folderCount');

      if (!user) {
        throw new Error('User not found');
      }

      const storageUsedBig = Number(user.storageUsed);
      const storageQuotaBig = Number(user.storageQuota);
      const availableSpaceBig = storageQuotaBig - storageUsedBig;

      // Calculate percentage safely
      const usagePercentage = storageQuotaBig > 0
        ? Number((storageUsedBig * 10000) / storageQuotaBig) / 100
        : 0;

      return {
        storageUsed: user.storageUsed,
        storageQuota: user.storageQuota,
        availableSpace: availableSpaceBig.toString(),
        usagePercentage: Math.round(usagePercentage * 100) / 100,
        fileCount: user.fileCount,
        folderCount: user.folderCount,
        // Human readable formats
        storageUsedFormatted: this.formatBytes(user.storageUsed),
        storageQuotaFormatted: this.formatBytes(user.storageQuota),
        availableSpaceFormatted: this.formatBytes(availableSpaceBig.toString())
      };
    } catch (error) {
      console.error('Error getting user storage info:', error);
      throw error;
    }
  }

  /**
   * Format bytes to human readable format
   * @param {string} bytesStr - Bytes as string to format
   * @returns {string} - Formatted string
   */
  formatBytes(bytesStr) {
    const bytes = Number(bytesStr);
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB'];

    let i = 0;
    let value = bytes;

    while (value >= k && i < sizes.length - 1) {
      value = value / k;
      i++;
    }

    // Convert to number for decimal formatting
    const formatted = value - Math.floor(value) > 0
      ? value.toFixed(2)
      : value;

    return formatted + ' ' + sizes[i];
  }

  /**
   * Recalculate and sync storage stats for a user (for maintenance/correction)
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Corrected storage info
   */
  async syncUserStorageStats(userId) {
    try {
      console.log(`Syncing storage stats for user: ${userId}`);

      const storageData = await this.calculateUserStorageUsed(userId);
      const folderCount = await this.calculateUserFolderCount(userId);

      const updatedUser = await UserModel.findByIdAndUpdate(
        userId,
        {
          $set: {
            storageUsed: storageData.totalSize, // Already a string
            fileCount: storageData.fileCount,
            folderCount: folderCount,
            updatedAt: Date.now()
          }
        },
        { new: true }
      ).select('storageUsed storageQuota fileCount folderCount');

      console.log(`Storage stats synced for user ${userId}:`, {
        storageUsed: storageData.totalSize,
        fileCount: storageData.fileCount,
        folderCount: folderCount
      });

      return updatedUser;
    } catch (error) {
      console.error('Error syncing user storage stats:', error);
      throw error;
    }
  }
}

module.exports = new StorageService();
