const cron = require('node-cron');
const fs = require('fs').promises;
const path = require('path');
const UserActivityLogModel = require('../models/userActivityLog');
const config = require('../config');

class LogCleanupService {
  constructor() {
    this.isRunning = false;
    this.cleanupJob = null;
    this.archiveJob = null;

    // Default configuration - can be overridden by config
    this.config = {
      // Retention periods in days
      retentionPeriods: {
        // Critical security events - keep longer
        security: config.get('activityLogs.retention.security') || 365, // 1 year
        // Authentication events
        authentication: config.get('activityLogs.retention.authentication') || 180, // 6 months
        // File operations
        fileOperations: config.get('activityLogs.retention.fileOperations') || 90, // 3 months
        // Navigation and search
        userBehavior: config.get('activityLogs.retention.userBehavior') || 30, // 1 month
        // System events
        system: config.get('activityLogs.retention.system') || 60, // 2 months
        // Error logs
        errors: config.get('activityLogs.retention.errors') || 90, // 3 months
        // Default for uncategorized
        default: config.get('activityLogs.retention.default') || 30 // 1 month
      },

      // Cleanup schedule (daily at 2 AM)
      cleanupSchedule: config.get('activityLogs.cleanup.schedule') || '0 2 * * *',

      // Archive schedule (weekly on Sunday at 3 AM)
      archiveSchedule: config.get('activityLogs.cleanup.archiveSchedule') || '0 3 * * 0',

      // Batch size for cleanup operations
      batchSize: config.get('activityLogs.cleanup.batchSize') || 1000,

      // Enable/disable archiving before deletion
      enableArchiving: config.get('activityLogs.cleanup.enableArchiving') || false,

      // Archive storage path (if archiving is enabled)
      archivePath: config.get('activityLogs.cleanup.archivePath') || './logs/archive'
    };

    // Map action types to categories for retention policies
    this.actionCategories = {
      // Security-related actions
      security: [
        UserActivityLogModel.ACTION_TYPES.LOGIN,
        UserActivityLogModel.ACTION_TYPES.LOGOUT,
        UserActivityLogModel.ACTION_TYPES.PASSWORD_CHANGE,
        UserActivityLogModel.ACTION_TYPES.PASSWORD_RESET,
        UserActivityLogModel.ACTION_TYPES.OAUTH_LOGIN
      ],

      // Authentication events
      authentication: [
        UserActivityLogModel.ACTION_TYPES.REGISTER,
        UserActivityLogModel.ACTION_TYPES.EMAIL_VERIFICATION
      ],

      // File operations
      fileOperations: [
        UserActivityLogModel.ACTION_TYPES.FILE_UPLOAD,
        UserActivityLogModel.ACTION_TYPES.FILE_DOWNLOAD,
        UserActivityLogModel.ACTION_TYPES.FILE_DELETE,
        UserActivityLogModel.ACTION_TYPES.FILE_RESTORE,
        UserActivityLogModel.ACTION_TYPES.FILE_SHARE,
        UserActivityLogModel.ACTION_TYPES.FILE_METADATA_VIEW,
        UserActivityLogModel.ACTION_TYPES.FILE_THUMBNAIL_VIEW
      ],

      // User behavior tracking
      userBehavior: [
        UserActivityLogModel.ACTION_TYPES.SEARCH_QUERY,
        UserActivityLogModel.ACTION_TYPES.SEARCH_FILTER,
        UserActivityLogModel.ACTION_TYPES.FOLDER_NAVIGATE,
        UserActivityLogModel.ACTION_TYPES.FOLDER_BROWSE,
        UserActivityLogModel.ACTION_TYPES.FILE_PREVIEW
      ],

      // System events
      system: [
        UserActivityLogModel.ACTION_TYPES.PROFILE_UPDATE,
        UserActivityLogModel.ACTION_TYPES.SETTINGS_CHANGE,
        UserActivityLogModel.ACTION_TYPES.STORAGE_SYNC,
        UserActivityLogModel.ACTION_TYPES.FOLDER_CREATE,
        UserActivityLogModel.ACTION_TYPES.FOLDER_DELETE
      ]
    };
  }

  /**
   * Start the cleanup service with scheduled jobs
   */
  start() {
    if (this.isRunning) {
      console.log('📋 Log cleanup service is already running');
      return;
    }

    console.log('📋 Starting log cleanup service...');

    // Schedule regular cleanup
    this.cleanupJob = cron.schedule(this.config.cleanupSchedule, async () => {
      console.log('📋 Running scheduled log cleanup...');
      await this.performCleanup();
    }, {
      scheduled: false,
      timezone: 'UTC'
    });

    // Schedule archiving if enabled
    if (this.config.enableArchiving) {
      this.archiveJob = cron.schedule(this.config.archiveSchedule, async () => {
        console.log('📋 Running scheduled log archiving...');
        await this.performArchiving();
      }, {
        scheduled: false,
        timezone: 'UTC'
      });
      this.archiveJob.start();
    }

    this.cleanupJob.start();
    this.isRunning = true;

    console.log('📋 Log cleanup service started successfully');
    console.log(`📋 Cleanup schedule: ${this.config.cleanupSchedule}`);
    if (this.config.enableArchiving) {
      console.log(`📋 Archive schedule: ${this.config.archiveSchedule}`);
    }
  }

  /**
   * Stop the cleanup service
   */
  stop() {
    if (!this.isRunning) {
      console.log('📋 Log cleanup service is not running');
      return;
    }

    console.log('📋 Stopping log cleanup service...');

    if (this.cleanupJob) {
      this.cleanupJob.stop();
      this.cleanupJob = null;
    }

    if (this.archiveJob) {
      this.archiveJob.stop();
      this.archiveJob = null;
    }

    this.isRunning = false;
    console.log('📋 Log cleanup service stopped');
  }

  /**
   * Get the retention period for a specific action type
   */
  getRetentionPeriod(actionType) {
    for (const [category, actions] of Object.entries(this.actionCategories)) {
      if (actions.includes(actionType)) {
        return this.config.retentionPeriods[category];
      }
    }
    return this.config.retentionPeriods.default;
  }

  /**
   * Perform cleanup of old logs based on retention policies
   */
  async performCleanup() {
    const startTime = Date.now();
    let totalDeleted = 0;

    try {
      console.log('📋 Starting log cleanup process...');

      // Process each category separately
      for (const [category, actions] of Object.entries(this.actionCategories)) {
        const retentionDays = this.config.retentionPeriods[category];
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

        console.log(`📋 Cleaning ${category} logs older than ${retentionDays} days (before ${cutoffDate.toISOString()})`);

        const deleted = await this.cleanupByCategory(actions, cutoffDate);
        totalDeleted += deleted;

        console.log(`📋 Deleted ${deleted} ${category} log entries`);
      }

      // Clean up any uncategorized logs using default retention
      const defaultRetentionDays = this.config.retentionPeriods.default;
      const defaultCutoffDate = new Date();
      defaultCutoffDate.setDate(defaultCutoffDate.getDate() - defaultRetentionDays);

      const allCategorizedActions = Object.values(this.actionCategories).flat();
      const uncategorizedDeleted = await this.cleanupUncategorized(allCategorizedActions, defaultCutoffDate);
      totalDeleted += uncategorizedDeleted;

      if (uncategorizedDeleted > 0) {
        console.log(`📋 Deleted ${uncategorizedDeleted} uncategorized log entries`);
      }

      const duration = Date.now() - startTime;
      console.log(`📋 Log cleanup completed: ${totalDeleted} entries deleted in ${duration}ms`);

      return {
        success: true,
        deletedCount: totalDeleted,
        duration: duration
      };

    } catch (error) {
      console.error('📋 Error during log cleanup:', error);
      return {
        success: false,
        error: error.message,
        deletedCount: totalDeleted
      };
    }
  }

  /**
   * Clean up logs for a specific category
   */
  async cleanupByCategory(actions, cutoffDate) {
    let totalDeleted = 0;

    try {
      const result = await UserActivityLogModel.deleteMany({
        actionType: { $in: actions },
        timestamp: { $lt: cutoffDate }
      });

      totalDeleted = result.deletedCount || 0;

    } catch (error) {
      console.error(`📋 Error cleaning up category logs:`, error);
      throw error;
    }

    return totalDeleted;
  }

  /**
   * Clean up uncategorized logs
   */
  async cleanupUncategorized(categorizedActions, cutoffDate) {
    let totalDeleted = 0;

    try {
      const result = await UserActivityLogModel.deleteMany({
        actionType: { $nin: categorizedActions },
        timestamp: { $lt: cutoffDate }
      });

      totalDeleted = result.deletedCount || 0;

    } catch (error) {
      console.error('📋 Error cleaning up uncategorized logs:', error);
      throw error;
    }

    return totalDeleted;
  }

  /**
   * Get cleanup statistics
   */
  async getCleanupStats() {
    try {
      const stats = {};
      const now = new Date();

      // Get counts for each category
      for (const [category, actions] of Object.entries(this.actionCategories)) {
        const retentionDays = this.config.retentionPeriods[category];
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

        const total = await UserActivityLogModel.countDocuments({
          actionType: { $in: actions }
        });

        const eligible = await UserActivityLogModel.countDocuments({
          actionType: { $in: actions },
          timestamp: { $lt: cutoffDate }
        });

        stats[category] = {
          total,
          eligibleForCleanup: eligible,
          retentionDays,
          cutoffDate: cutoffDate.toISOString()
        };
      }

      // Get uncategorized stats
      const allCategorizedActions = Object.values(this.actionCategories).flat();
      const defaultCutoffDate = new Date();
      defaultCutoffDate.setDate(defaultCutoffDate.getDate() - this.config.retentionPeriods.default);

      const uncategorizedTotal = await UserActivityLogModel.countDocuments({
        actionType: { $nin: allCategorizedActions }
      });

      const uncategorizedEligible = await UserActivityLogModel.countDocuments({
        actionType: { $nin: allCategorizedActions },
        timestamp: { $lt: defaultCutoffDate }
      });

      stats.uncategorized = {
        total: uncategorizedTotal,
        eligibleForCleanup: uncategorizedEligible,
        retentionDays: this.config.retentionPeriods.default,
        cutoffDate: defaultCutoffDate.toISOString()
      };

      return stats;

    } catch (error) {
      console.error('📋 Error getting cleanup stats:', error);
      throw error;
    }
  }

  /**
   * Perform manual cleanup (for admin interface)
   */
  async performManualCleanup(options = {}) {
    const {
      category = null,
      olderThanDays = null,
      dryRun = false
    } = options;

    try {
      if (category && !this.actionCategories[category]) {
        throw new Error(`Invalid category: ${category}`);
      }

      let query = {};
      let retentionDays;

      if (category) {
        query.actionType = { $in: this.actionCategories[category] };
        retentionDays = olderThanDays || this.config.retentionPeriods[category];
      } else {
        retentionDays = olderThanDays || this.config.retentionPeriods.default;
      }

      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays);
      query.timestamp = { $lt: cutoffDate };

      if (dryRun) {
        const count = await UserActivityLogModel.countDocuments(query);
        return {
          success: true,
          dryRun: true,
          wouldDelete: count,
          query: query
        };
      } else {
        const result = await UserActivityLogModel.deleteMany(query);
        return {
          success: true,
          dryRun: false,
          deletedCount: result.deletedCount || 0,
          query: query
        };
      }

    } catch (error) {
      console.error('📋 Error during manual cleanup:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Perform archiving of logs before cleanup
   */
  async performArchiving() {
    const startTime = Date.now();
    let totalArchived = 0;

    try {
      console.log('📋 Starting log archiving process...');

      // Ensure archive directory exists
      await this.ensureArchiveDirectory();

      // Archive each category separately
      for (const [category, actions] of Object.entries(this.actionCategories)) {
        const retentionDays = this.config.retentionPeriods[category];
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

        console.log(`📋 Archiving ${category} logs older than ${retentionDays} days`);

        const archived = await this.archiveByCategory(category, actions, cutoffDate);
        totalArchived += archived;

        console.log(`📋 Archived ${archived} ${category} log entries`);
      }

      const duration = Date.now() - startTime;
      console.log(`📋 Log archiving completed: ${totalArchived} entries archived in ${duration}ms`);

      return {
        success: true,
        archivedCount: totalArchived,
        duration: duration
      };

    } catch (error) {
      console.error('📋 Error during log archiving:', error);
      return {
        success: false,
        error: error.message,
        archivedCount: totalArchived
      };
    }
  }

  /**
   * Archive logs for a specific category
   */
  async archiveByCategory(category, actions, cutoffDate) {
    let totalArchived = 0;

    try {
      // Find logs to archive
      const logsToArchive = await UserActivityLogModel.find({
        actionType: { $in: actions },
        timestamp: { $lt: cutoffDate }
      }).lean();

      if (logsToArchive.length === 0) {
        return 0;
      }

      // Create archive file
      const archiveFileName = `${category}_${cutoffDate.toISOString().split('T')[0]}_${Date.now()}.json`;
      const archiveFilePath = path.join(this.config.archivePath, archiveFileName);

      // Write logs to archive file
      await fs.writeFile(archiveFilePath, JSON.stringify(logsToArchive, null, 2));

      totalArchived = logsToArchive.length;
      console.log(`📋 Archived ${totalArchived} logs to ${archiveFileName}`);

    } catch (error) {
      console.error(`📋 Error archiving ${category} logs:`, error);
      throw error;
    }

    return totalArchived;
  }

  /**
   * Ensure archive directory exists
   */
  async ensureArchiveDirectory() {
    try {
      await fs.access(this.config.archivePath);
    } catch (error) {
      // Directory doesn't exist, create it
      await fs.mkdir(this.config.archivePath, { recursive: true });
      console.log(`📋 Created archive directory: ${this.config.archivePath}`);
    }
  }

  /**
   * Get archive information
   */
  async getArchiveInfo() {
    try {
      await this.ensureArchiveDirectory();

      const files = await fs.readdir(this.config.archivePath);
      const archiveFiles = files.filter(file => file.endsWith('.json'));

      const archiveInfo = [];

      for (const file of archiveFiles) {
        const filePath = path.join(this.config.archivePath, file);
        const stats = await fs.stat(filePath);

        archiveInfo.push({
          filename: file,
          size: stats.size,
          created: stats.birthtime,
          modified: stats.mtime
        });
      }

      return {
        archivePath: this.config.archivePath,
        totalFiles: archiveFiles.length,
        files: archiveInfo.sort((a, b) => b.created - a.created)
      };

    } catch (error) {
      console.error('📋 Error getting archive info:', error);
      throw error;
    }
  }

  /**
   * Clean up old archive files
   */
  async cleanupArchives(olderThanDays = 365) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

      const files = await fs.readdir(this.config.archivePath);
      const archiveFiles = files.filter(file => file.endsWith('.json'));

      let deletedCount = 0;

      for (const file of archiveFiles) {
        const filePath = path.join(this.config.archivePath, file);
        const stats = await fs.stat(filePath);

        if (stats.birthtime < cutoffDate) {
          await fs.unlink(filePath);
          deletedCount++;
          console.log(`📋 Deleted old archive file: ${file}`);
        }
      }

      return {
        success: true,
        deletedCount: deletedCount
      };

    } catch (error) {
      console.error('📋 Error cleaning up archives:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

module.exports = new LogCleanupService();
