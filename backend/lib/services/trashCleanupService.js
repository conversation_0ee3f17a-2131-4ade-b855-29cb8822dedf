const cron = require('node-cron');

class TrashCleanupService {
  constructor() {
    this.isRunning = false;
    this.lastCleanup = null;
    this.cleanupStats = {
      totalRuns: 0,
      totalItemsCleaned: 0,
      lastRunDate: null,
      lastRunStats: null
    };
  }

  /**
   * Start the automatic cleanup scheduler
   * Runs daily at 2:00 AM
   */
  startScheduler() {
    if (this.isRunning) {
      console.log('Trash cleanup scheduler is already running');
      return;
    }

    // Schedule to run daily at 2:00 AM
    this.scheduledTask = cron.schedule('0 2 * * *', async () => {
      await this.runCleanup();
    }, {
      scheduled: true,
      timezone: "UTC"
    });

    this.isRunning = true;
    console.log('Trash cleanup scheduler started - will run daily at 2:00 AM UTC');
  }

  /**
   * Stop the automatic cleanup scheduler
   */
  stopScheduler() {
    if (this.scheduledTask) {
      this.scheduledTask.stop();
      this.isRunning = false;
      console.log('Trash cleanup scheduler stopped');
    }
  }

  /**
   * Manually run the cleanup process
   */
  async runCleanup() {
    try {
      console.log('Starting trash cleanup process...');
      
      const FileModel = require('../models/file');
      const FolderModel = require('../models/folder');

      // Calculate date 30 days ago
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      // Find items deleted more than 30 days ago that haven't been permanently deleted
      const oldDeletedFiles = await FileModel.find({
        isDeleted: true,
        deletedAt: { $lt: thirtyDaysAgo },
        permanentlyDeletedAt: { $exists: false }
      }).select('_id ownerId originalFileName fileSize').lean();

      const oldDeletedFolders = await FolderModel.find({
        isDeleted: true,
        deletedAt: { $lt: thirtyDaysAgo },
        permanentlyDeletedAt: { $exists: false }
      }).select('_id ownerId folderName').lean();

      if (oldDeletedFiles.length === 0 && oldDeletedFolders.length === 0) {
        console.log('No items found for cleanup');
        this.updateStats(0, 0, 0);
        return { totalCleaned: 0, filesCleaned: 0, foldersCleaned: 0 };
      }

      // Permanently delete old files
      const fileCleanupResult = await FileModel.updateMany(
        {
          isDeleted: true,
          deletedAt: { $lt: thirtyDaysAgo },
          permanentlyDeletedAt: { $exists: false }
        },
        {
          $set: { permanentlyDeletedAt: new Date() }
        }
      );

      // Permanently delete old folders
      const folderCleanupResult = await FolderModel.updateMany(
        {
          isDeleted: true,
          deletedAt: { $lt: thirtyDaysAgo },
          permanentlyDeletedAt: { $exists: false }
        },
        {
          $set: { permanentlyDeletedAt: new Date() }
        }
      );

      const totalCleaned = fileCleanupResult.modifiedCount + folderCleanupResult.modifiedCount;
      
      // Calculate total storage freed
      const totalStorageFreed = oldDeletedFiles.reduce((total, file) => total + (file.fileSize || 0), 0);

      const result = {
        totalCleaned,
        filesCleaned: fileCleanupResult.modifiedCount,
        foldersCleaned: folderCleanupResult.modifiedCount,
        storageFreed: totalStorageFreed,
        cleanupDate: thirtyDaysAgo.toISOString(),
        cleanedItems: {
          files: oldDeletedFiles.map(f => ({ id: f._id, name: f.originalFileName, owner: f.ownerId })),
          folders: oldDeletedFolders.map(f => ({ id: f._id, name: f.folderName, owner: f.ownerId }))
        }
      };

      this.updateStats(totalCleaned, fileCleanupResult.modifiedCount, folderCleanupResult.modifiedCount, result);

      console.log(`Trash cleanup completed: ${totalCleaned} items permanently deleted (${fileCleanupResult.modifiedCount} files, ${folderCleanupResult.modifiedCount} folders)`);
      
      if (global.logger) {
        global.logger.logInfo(`Auto-cleanup completed: ${totalCleaned} items permanently deleted (${fileCleanupResult.modifiedCount} files, ${folderCleanupResult.modifiedCount} folders)`);
      }

      return result;

    } catch (error) {
      console.error('Trash cleanup error:', error);
      if (global.logger) {
        global.logger.logInfo(['trash cleanup error', error.message], __dirname);
      }
      throw error;
    }
  }

  /**
   * Update cleanup statistics
   */
  updateStats(totalCleaned, filesCleaned, foldersCleaned, fullStats = null) {
    this.cleanupStats.totalRuns += 1;
    this.cleanupStats.totalItemsCleaned += totalCleaned;
    this.cleanupStats.lastRunDate = new Date();
    this.cleanupStats.lastRunStats = {
      totalCleaned,
      filesCleaned,
      foldersCleaned,
      runDate: new Date()
    };

    if (fullStats) {
      this.cleanupStats.lastRunStats = { ...this.cleanupStats.lastRunStats, ...fullStats };
    }
  }

  /**
   * Get cleanup statistics
   */
  getStats() {
    return {
      ...this.cleanupStats,
      isRunning: this.isRunning,
      nextRun: this.isRunning ? 'Daily at 2:00 AM UTC' : 'Not scheduled'
    };
  }

  /**
   * Check if cleanup is needed (for manual checks)
   */
  async checkCleanupNeeded() {
    try {
      const FileModel = require('../models/file');
      const FolderModel = require('../models/folder');

      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const oldFilesCount = await FileModel.countDocuments({
        isDeleted: true,
        deletedAt: { $lt: thirtyDaysAgo },
        permanentlyDeletedAt: { $exists: false }
      });

      const oldFoldersCount = await FolderModel.countDocuments({
        isDeleted: true,
        deletedAt: { $lt: thirtyDaysAgo },
        permanentlyDeletedAt: { $exists: false }
      });

      return {
        needed: (oldFilesCount + oldFoldersCount) > 0,
        itemsToClean: oldFilesCount + oldFoldersCount,
        filesCount: oldFilesCount,
        foldersCount: oldFoldersCount,
        cutoffDate: thirtyDaysAgo
      };

    } catch (error) {
      console.error('Error checking cleanup needed:', error);
      return { needed: false, error: error.message };
    }
  }
}

// Create singleton instance
const trashCleanupService = new TrashCleanupService();

module.exports = trashCleanupService;
