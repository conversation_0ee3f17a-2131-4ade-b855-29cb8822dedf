const logCleanupService = require('../services/logCleanupService');

/**
 * Initialize log cleanup service on application startup
 */
function initializeLogCleanup() {
  try {
    console.log('📋 Initializing log cleanup service...');
    
    // Start the cleanup service
    logCleanupService.start();
    
    // Handle graceful shutdown
    process.on('SIGINT', () => {
      console.log('📋 Received SIGINT, stopping log cleanup service...');
      logCleanupService.stop();
    });
    
    process.on('SIGTERM', () => {
      console.log('📋 Received SIGTERM, stopping log cleanup service...');
      logCleanupService.stop();
    });
    
    console.log('📋 Log cleanup service initialized successfully');
    
  } catch (error) {
    console.error('📋 Failed to initialize log cleanup service:', error);
    // Don't crash the application if log cleanup fails to start
  }
}

module.exports = {
  initializeLogCleanup
};
