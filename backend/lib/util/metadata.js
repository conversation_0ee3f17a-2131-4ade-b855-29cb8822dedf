const crypto = require('crypto');
const fs = require('fs');
const UAParser = require('ua-parser-js');
const geoip = require('geoip-lite');
const exifr = require('exifr');
const sharp = require('sharp');

/**
 * Utility functions for collecting and processing file upload metadata
 */
module.exports = {
  /**
   * Calculate file hashes (MD5 and SHA256) with enhanced debugging
   * @param {string} filePath - Path to the file
   * @returns {Promise<Object>} - Object containing md5 and sha256 hashes with metadata
   */
  calculateFileHashes: (filePath) => {
    return new Promise((resolve, reject) => {
      try {
        const md5Hash = crypto.createHash('md5');
        const sha256Hash = crypto.createHash('sha256');

        // Get file stats for debugging
        const stats = fs.statSync(filePath);
        const startTime = Date.now();
        let bytesRead = 0;

        const stream = fs.createReadStream(filePath, {
          highWaterMark: 64 * 1024 // 64KB chunks for consistent reading
        });

        stream.on('data', (data) => {
          md5Hash.update(data);
          sha256Hash.update(data);
          bytesRead += data.length;
        });

        stream.on('end', () => {
          const endTime = Date.now();
          const result = {
            md5: md5Hash.digest('hex'),
            sha256: sha256Hash.digest('hex'),
            // Debug information
            debug: {
              fileSize: stats.size,
              bytesRead: bytesRead,
              processingTime: endTime - startTime,
              mtime: stats.mtime.toISOString(),
              ctime: stats.ctime.toISOString(),
              filePath: filePath
            }
          };

          console.log(`Hash calculation completed for ${filePath}:`);
          console.log(`- File size: ${stats.size} bytes`);
          console.log(`- Bytes read: ${bytesRead} bytes`);
          console.log(`- Processing time: ${endTime - startTime}ms`);
          console.log(`- MD5: ${result.md5}`);
          console.log(`- SHA256: ${result.sha256}`);

          // Log first and last few bytes for debugging
          try {
            const buffer = fs.readFileSync(filePath);
            const firstBytes = buffer.subarray(0, Math.min(32, buffer.length));
            const lastBytes = buffer.subarray(Math.max(0, buffer.length - 32));
            console.log(`- First 32 bytes: ${firstBytes.toString('hex')}`);
            console.log(`- Last 32 bytes: ${lastBytes.toString('hex')}`);
          } catch (err) {
            console.log(`- Could not read file bytes: ${err.message}`);
          }

          resolve(result);
        });

        stream.on('error', (error) => {
          console.error(`Error reading file for hash calculation: ${filePath}`, error);
          reject(error);
        });
      } catch (error) {
        console.error(`Error in calculateFileHashes: ${filePath}`, error);
        reject(error);
      }
    });
  },

  /**
   * Extract client IP address from request
   * @param {Object} req - Express request object
   * @returns {string} - Client IP address
   */
  getClientIP: (req) => {
    return req.headers['x-forwarded-for'] ||
           req.headers['x-real-ip'] ||
           req.connection.remoteAddress ||
           req.socket.remoteAddress ||
           (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
           req.ip ||
           'unknown';
  },

  /**
   * Parse User Agent string to extract device/browser information
   * @param {string} userAgent - User Agent string
   * @returns {Object} - Parsed device and browser information
   */
  parseUserAgent: (userAgent) => {
    if (!userAgent) {
      return {
        platform: 'unknown',
        browser: 'unknown',
        browserVersion: 'unknown',
        isMobile: false,
        isTablet: false
      };
    }

    const parser = new UAParser(userAgent);
    const result = parser.getResult();

    return {
      platform: result.os.name || 'unknown',
      browser: result.browser.name || 'unknown',
      browserVersion: result.browser.version || 'unknown',
      isMobile: result.device.type === 'mobile',
      isTablet: result.device.type === 'tablet'
    };
  },

  /**
   * Get geolocation information from IP address
   * @param {string} ipAddress - IP address
   * @returns {Object} - Geolocation information
   */
  getGeolocationFromIP: (ipAddress) => {
    try {
      // Skip private/local IPs
      if (ipAddress === 'unknown' ||
          ipAddress.startsWith('127.') ||
          ipAddress.startsWith('192.168.') ||
          ipAddress.startsWith('10.') ||
          ipAddress.includes('::1')) {
        return null;
      }

      const geo = geoip.lookup(ipAddress);
      if (!geo) return null;

      return {
        city: geo.city || 'unknown',
        country: geo.country || 'unknown',
        latitude: geo.ll ? geo.ll[0] : null,
        longitude: geo.ll ? geo.ll[1] : null,
        timezone: geo.timezone || 'unknown'
      };
    } catch (error) {
      console.error('Error getting geolocation from IP:', error);
      return null;
    }
  },

  /**
   * Safely parse a value to number, handling various EXIF data types
   * @param {any} value - Value to parse
   * @returns {number|null} - Parsed number or null
   */
  safeParseNumber(value) {
    if (value === null || value === undefined) return null;

    // If already a number
    if (typeof value === 'number') return value;

    // If it's a string, try to parse
    if (typeof value === 'string') {
      // Handle special EXIF string patterns
      if (value === 'Pattern' || value === 'Auto' || value === 'Standard' || value === 'Horizontal (normal)') {
        return null; // These are descriptive strings, not numbers
      }

      const parsed = parseFloat(value);
      return isNaN(parsed) ? null : parsed;
    }

    // For objects or arrays, return null
    return null;
  },

  /**
   * Extract EXIF data from image/video files
   * @param {string} filePath - Path to the file
   * @param {string} mimeType - MIME type of the file
   * @returns {Promise<Object>} - EXIF data including GPS, camera info, etc.
   */
  async extractExifData(filePath, mimeType) {
    try {
      // Check if file is an image or video that might contain EXIF
      const isImage = mimeType.startsWith('image/');
      const isVideo = mimeType.startsWith('video/');

      if (!isImage && !isVideo) {
        return null;
      }

      // Extract EXIF data using exifr
      const exifData = await exifr.parse(filePath, {
        // Extract all available tags
        tiff: true,
        exif: true,
        gps: true,
        icc: true,
        iptc: true,
        xmp: true,
        // Include maker notes for camera-specific data
        makerNote: true,
        // Include thumbnail data
        thumbnail: true
      });

      if (!exifData) {
        return null;
      }

      // Process and organize EXIF data
      const processedExif = {
        // Camera information
        camera: {
          make: exifData.Make || null,
          model: exifData.Model || null,
          software: exifData.Software || null,
          lens: exifData.LensModel || exifData.LensMake || null,
          serialNumber: exifData.SerialNumber || null
        },

        // Photo settings
        photoSettings: {
          iso: this.safeParseNumber(exifData.ISO),
          fNumber: this.safeParseNumber(exifData.FNumber),
          exposureTime: this.safeParseNumber(exifData.ExposureTime),
          focalLength: this.safeParseNumber(exifData.FocalLength),
          focalLengthIn35mm: this.safeParseNumber(exifData.FocalLengthIn35mmFormat),
          flash: this.safeParseNumber(exifData.Flash),
          whiteBalance: this.safeParseNumber(exifData.WhiteBalance),
          meteringMode: this.safeParseNumber(exifData.MeteringMode),
          exposureMode: this.safeParseNumber(exifData.ExposureMode),
          sceneCaptureType: this.safeParseNumber(exifData.SceneCaptureType)
        },

        // Date and time information
        dateTime: {
          dateTimeOriginal: exifData.DateTimeOriginal || null,
          dateTimeDigitized: exifData.DateTimeDigitized || null,
          modifyDate: exifData.ModifyDate || null,
          createDate: exifData.CreateDate || null,
          // Subsecond precision
          subsecTimeOriginal: exifData.SubSecTimeOriginal || null,
          subsecTimeDigitized: exifData.SubSecTimeDigitized || null
        },

        // GPS information
        gps: {
          latitude: this.safeParseNumber(exifData.latitude),
          longitude: this.safeParseNumber(exifData.longitude),
          altitude: this.safeParseNumber(exifData.GPSAltitude),
          altitudeRef: this.safeParseNumber(exifData.GPSAltitudeRef),
          speed: this.safeParseNumber(exifData.GPSSpeed),
          speedRef: exifData.GPSSpeedRef || null,
          direction: this.safeParseNumber(exifData.GPSImgDirection),
          directionRef: exifData.GPSImgDirectionRef || null,
          timestamp: exifData.GPSTimeStamp || null,
          datestamp: exifData.GPSDateStamp || null,
          // Location names if available
          gpsProcessingMethod: exifData.GPSProcessingMethod || null,
          gpsAreaInformation: exifData.GPSAreaInformation || null
        },

        // Image dimensions and quality
        image: {
          width: this.safeParseNumber(exifData.ExifImageWidth || exifData.ImageWidth),
          height: this.safeParseNumber(exifData.ExifImageHeight || exifData.ImageHeight),
          orientation: this.safeParseNumber(exifData.Orientation),
          colorSpace: this.safeParseNumber(exifData.ColorSpace),
          compression: this.safeParseNumber(exifData.Compression),
          bitsPerSample: this.safeParseNumber(exifData.BitsPerSample),
          photometricInterpretation: this.safeParseNumber(exifData.PhotometricInterpretation)
        },

        // Video-specific data (if applicable)
        video: isVideo ? {
          duration: this.safeParseNumber(exifData.Duration),
          frameRate: this.safeParseNumber(exifData.VideoFrameRate),
          bitrate: this.safeParseNumber(exifData.AvgBitrate),
          codec: exifData.VideoCodec || null,
          audioCodec: exifData.AudioCodec || null
        } : null,

        // Additional metadata
        additional: {
          artist: exifData.Artist || null,
          copyright: exifData.Copyright || null,
          description: exifData.ImageDescription || null,
          userComment: exifData.UserComment || null,
          keywords: exifData.Keywords || null,
          subject: exifData.Subject || null,
          title: exifData.Title || null
        },

        // Raw EXIF data for debugging
        raw: process.env.NODE_ENV === 'development' ? exifData : null
      };

      // Clean up null values
      const cleanObject = (obj) => {
        if (obj === null || obj === undefined) return null;
        if (typeof obj !== 'object') return obj;

        const cleaned = {};
        for (const [key, value] of Object.entries(obj)) {
          const cleanedValue = cleanObject(value);
          if (cleanedValue !== null && cleanedValue !== undefined) {
            cleaned[key] = cleanedValue;
          }
        }

        return Object.keys(cleaned).length > 0 ? cleaned : null;
      };

      return cleanObject(processedExif);

    } catch (error) {
      console.error('Error extracting EXIF data:', error);
      return null;
    }
  },

  /**
   * Get additional image metadata using Sharp
   * @param {string} filePath - Path to the image file
   * @param {string} mimeType - MIME type of the file
   * @returns {Promise<Object>} - Additional image metadata
   */
  async getImageMetadata(filePath, mimeType) {
    try {
      if (!mimeType.startsWith('image/')) {
        return null;
      }

      const metadata = await sharp(filePath).metadata();

      return {
        format: metadata.format,
        width: metadata.width,
        height: metadata.height,
        channels: metadata.channels,
        depth: metadata.depth,
        density: metadata.density,
        hasProfile: metadata.hasProfile,
        hasAlpha: metadata.hasAlpha,
        isProgressive: metadata.isProgressive,
        compression: metadata.compression,
        resolutionUnit: metadata.resolutionUnit,
        size: metadata.size
      };
    } catch (error) {
      console.error('Error getting image metadata with Sharp:', error);
      return null;
    }
  },

  /**
   * Generate unique session ID
   * @returns {string} - Unique session ID
   */
  generateSessionId: () => {
    return crypto.randomBytes(16).toString('hex') + '-' + Date.now();
  },

  /**
   * Calculate upload speed
   * @param {number} fileSize - File size in bytes
   * @param {number} uploadDuration - Upload duration in milliseconds
   * @returns {number} - Upload speed in bytes per second
   */
  calculateUploadSpeed: (fileSize, uploadDuration) => {
    if (!uploadDuration || uploadDuration === 0) return 0;
    return Math.round((fileSize * 1000) / uploadDuration); // bytes per second
  },

  /**
   * Detect upload source based on request headers and body
   * @param {Object} req - Express request object
   * @returns {string} - Upload source type
   */
  detectUploadSource: (req) => {
    const userAgent = req.headers['user-agent'] || '';
    const referer = req.headers.referer || '';

    // Check if it's from mobile app
    if (userAgent.includes('Mobile') || userAgent.includes('Android') || userAgent.includes('iPhone')) {
      return 'mobile';
    }

    // Check if it's API call
    if (userAgent.includes('curl') || userAgent.includes('Postman') || userAgent.includes('axios')) {
      return 'api';
    }

    // Check if it's drag and drop (this would need to be set by frontend)
    if (req.body.uploadSource) {
      return req.body.uploadSource;
    }

    return 'web';
  },

  /**
   * Collect comprehensive upload metadata from request
   * @param {Object} req - Express request object
   * @param {Object} file - Multer file object
   * @param {Date} uploadStartTime - Upload start time
   * @returns {Promise<Object>} - Complete upload metadata
   */
  async collectUploadMetadata(req, file, uploadStartTime) {
    const uploadEndTime = new Date();
    const uploadDuration = uploadEndTime.getTime() - uploadStartTime.getTime();

    const ipAddress = this.getClientIP(req);
    const userAgent = req.headers['user-agent'] || '';
    const deviceInfo = this.parseUserAgent(userAgent);
    const geoFromIP = this.getGeolocationFromIP(ipAddress);

    // Calculate file hashes
    let fileHashes = { md5: null, sha256: null };
    try {
      fileHashes = await this.calculateFileHashes(file.path);
    } catch (error) {
      console.error('Error calculating file hashes:', error);
    }

    // Extract EXIF data from images/videos
    let exifData = null;
    try {
      exifData = await this.extractExifData(file.path, file.mimetype);
    } catch (error) {
      console.error('Error extracting EXIF data:', error);
    }

    // Get additional image metadata
    let imageMetadata = null;
    try {
      imageMetadata = await this.getImageMetadata(file.path, file.mimetype);
    } catch (error) {
      console.error('Error getting image metadata:', error);
    }

    const metadata = {
      // Client information
      ipAddress: ipAddress,
      userAgent: userAgent,
      referer: req.headers.referer || null,

      // Geolocation from IP (client geolocation disabled to avoid bothering users)
      geolocation: {
        // IP-based geolocation only
        latitude: geoFromIP ? geoFromIP.latitude : null,
        longitude: geoFromIP ? geoFromIP.longitude : null,
        accuracy: null, // Not available from IP geolocation
        timestamp: null, // Not available from IP geolocation
        city: geoFromIP ? geoFromIP.city : null,
        country: geoFromIP ? geoFromIP.country : null
      },

      // Device/Browser information
      deviceInfo: {
        ...deviceInfo,
        screenResolution: req.body.screenResolution || null,
        timezone: req.body.timezone || (geoFromIP ? geoFromIP.timezone : null),
        language: req.body.language || req.headers['accept-language']?.split(',')[0] || null
      },

      // Upload session details
      uploadSession: {
        sessionId: this.generateSessionId(),
        uploadStartTime: uploadStartTime,
        uploadEndTime: uploadEndTime,
        uploadDuration: uploadDuration,
        uploadSpeed: this.calculateUploadSpeed(file.size, uploadDuration),
        retryCount: parseInt(req.body.retryCount) || 0,
        uploadSource: req.body.uploadSource || this.detectUploadSource(req)
      },

      // Network information (if provided by client)
      networkInfo: {
        connectionType: req.body.connectionType || null,
        downlink: req.body.downlink ? parseFloat(req.body.downlink) : null,
        effectiveType: req.body.effectiveType || null,
        rtt: req.body.rtt ? parseFloat(req.body.rtt) : null
      }
    };

    return {
      fileHash: fileHashes,
      uploadMetadata: metadata,
      exifData: exifData,
      imageMetadata: imageMetadata
    };
  },

  /**
   * Format metadata for logging
   * @param {Object} metadata - Upload metadata
   * @returns {string} - Formatted metadata string
   */
  formatMetadataForLog: (metadata) => {
    const { uploadMetadata, exifData } = metadata;
    const exifLocation = exifData?.gps?.latitude ? `EXIF GPS: ${exifData.gps.latitude.toFixed(4)},${exifData.gps.longitude.toFixed(4)}` : null;
    const ipLocation = uploadMetadata.geolocation.city ? `IP: ${uploadMetadata.geolocation.city}` : 'Location: unknown';

    return `IP: ${uploadMetadata.ipAddress}, ` +
           `Device: ${uploadMetadata.deviceInfo.platform} ${uploadMetadata.deviceInfo.browser}, ` +
           `${exifLocation || ipLocation}, ` +
           `Source: ${uploadMetadata.uploadSession.uploadSource}, ` +
           `Speed: ${Math.round(uploadMetadata.uploadSession.uploadSpeed / 1024)}KB/s`;
  },

  /**
   * Check for duplicate files in a folder
   * @param {string} md5Hash - MD5 hash of the file
   * @param {string} sha256Hash - SHA256 hash of the file
   * @param {string} userId - User ID
   * @param {string} parentId - Parent folder ID (null for root)
   * @returns {Promise<Object|null>} - Duplicate file info or null
   */
  async checkForDuplicates(md5Hash, sha256Hash, userId, parentId) {
    try {
      const FileModel = require('../models/file');

      // Build query to check for duplicates
      const duplicateQuery = {
        isDeleted: false,
        ownerId: userId,
        parentId: parentId || null,
        $or: []
      };

      if (md5Hash) {
        duplicateQuery.$or.push({ 'fileHash.md5': md5Hash });
      }
      if (sha256Hash) {
        duplicateQuery.$or.push({ 'fileHash.sha256': sha256Hash });
      }

      // If no hashes provided, return null
      if (duplicateQuery.$or.length === 0) {
        return null;
      }

      const existingFile = await FileModel.findOne(duplicateQuery).lean();
      return existingFile;
    } catch (error) {
      console.error('Error checking for duplicates:', error);
      return null;
    }
  },

  /**
   * Compare file hashes and provide detailed analysis
   * @param {Object} hash1 - First file hash object
   * @param {Object} hash2 - Second file hash object
   * @returns {Object} - Comparison result
   */
  compareFileHashes(hash1, hash2) {
    const result = {
      isIdentical: false,
      md5Match: false,
      sha256Match: false,
      analysis: []
    };

    if (hash1.md5 && hash2.md5) {
      result.md5Match = hash1.md5 === hash2.md5;
      if (result.md5Match) {
        result.analysis.push('MD5 hashes match');
      } else {
        result.analysis.push('MD5 hashes differ');
      }
    }

    if (hash1.sha256 && hash2.sha256) {
      result.sha256Match = hash1.sha256 === hash2.sha256;
      if (result.sha256Match) {
        result.analysis.push('SHA256 hashes match');
      } else {
        result.analysis.push('SHA256 hashes differ');
      }
    }

    // Files are considered identical if both hashes match (when available)
    result.isIdentical = (result.md5Match || !hash1.md5 || !hash2.md5) &&
                        (result.sha256Match || !hash1.sha256 || !hash2.sha256) &&
                        (result.md5Match || result.sha256Match);

    return result;
  }
};
