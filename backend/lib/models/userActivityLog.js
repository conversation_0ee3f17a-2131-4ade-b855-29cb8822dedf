const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const mongoConnections = require('../connections/mongo');

// Define action types enum for better data consistency
const ACTION_TYPES = {
  // Authentication actions
  LOGIN: 'login',
  LOGOUT: 'logout',
  REGISTER: 'register',
  PASSWORD_CHANGE: 'password_change',
  PASSWORD_RESET: 'password_reset',
  EMAIL_VERIFICATION: 'email_verification',
  OAUTH_LOGIN: 'oauth_login',
  
  // File operations
  FILE_UPLOAD: 'file_upload',
  FILE_DOWNLOAD: 'file_download',
  FILE_PREVIEW: 'file_preview',
  FILE_DELETE: 'file_delete',
  FILE_RESTORE: 'file_restore',
  FILE_SHARE: 'file_share',
  FILE_METADATA_VIEW: 'file_metadata_view',
  FILE_THUMBNAIL_VIEW: 'file_thumbnail_view',
  
  // Folder operations
  FOLDER_CREATE: 'folder_create',
  FOLDER_DELETE: 'folder_delete',
  FOLDER_NAVIGATE: 'folder_navigate',
  FOLDER_BROWSE: 'folder_browse',
  
  // Search and discovery
  SEARCH_QUERY: 'search_query',
  SEARCH_FILTER: 'search_filter',
  
  // System actions
  PROFILE_UPDATE: 'profile_update',
  SETTINGS_CHANGE: 'settings_change',
  STORAGE_SYNC: 'storage_sync',
  
  // Admin actions
  ADMIN_USER_VIEW: 'admin_user_view',
  ADMIN_LOG_VIEW: 'admin_log_view',
  ADMIN_SYSTEM_CONFIG: 'admin_system_config'
};

// Define status types
const STATUS_TYPES = {
  SUCCESS: 'success',
  FAILURE: 'failure',
  PARTIAL: 'partial',
  ERROR: 'error'
};

const UserActivityLogSchema = new mongoose.Schema({
  // Core identification
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  
  // Action details
  action: {
    type: String,
    required: true,
    enum: Object.values(ACTION_TYPES),
    index: true
  },
  
  status: {
    type: String,
    required: true,
    enum: Object.values(STATUS_TYPES),
    default: STATUS_TYPES.SUCCESS,
    index: true
  },
  
  // Timestamps (ISO format for better querying)
  timestamp: {
    type: Date,
    required: true,
    default: Date.now,
    index: true
  },
  
  // Resource information
  resourceType: {
    type: String,
    enum: ['file', 'folder', 'user', 'system', 'auth', 'search'],
    index: true
  },
  
  resourceId: {
    type: String, // Can be ObjectId string or other identifier
    index: true
  },
  
  resourcePath: {
    type: String, // File path, folder path, or route path
    index: true
  },
  
  resourceName: {
    type: String // File name, folder name, etc.
  },
  
  // Request context
  requestMetadata: {
    method: String, // HTTP method
    url: String, // Request URL
    userAgent: String,
    ipAddress: String,
    referer: String,
    
    // Geolocation (if available)
    geolocation: {
      latitude: Number,
      longitude: Number,
      accuracy: Number,
      city: String,
      country: String,
      address: String
    },
    
    // Performance metrics
    responseTime: Number, // in milliseconds
    requestSize: Number, // in bytes
    responseSize: Number // in bytes
  },
  
  // Session information
  sessionInfo: {
    sessionId: String,
    tokenId: String, // JWT token identifier
    deviceType: String, // mobile, desktop, tablet
    browserName: String,
    browserVersion: String,
    osName: String,
    osVersion: String
  },
  
  // Action-specific metadata
  actionMetadata: {
    type: Schema.Types.Mixed,
    default: {}
  },
  
  // Additional context data
  additionalData: {
    // File-specific data
    fileSize: Number,
    mimeType: String,
    fileExtension: String,
    
    // Search-specific data
    searchQuery: String,
    searchFilters: Schema.Types.Mixed,
    searchResultsCount: Number,
    
    // Authentication-specific data
    authProvider: String, // local, google, telegram
    loginMethod: String,
    
    // Error information
    errorCode: String,
    errorMessage: String,
    errorStack: String,
    
    // Success metrics
    processingTime: Number,
    cacheHit: Boolean,
    
    // Privacy and compliance
    dataRetentionCategory: {
      type: String,
      enum: ['essential', 'functional', 'analytics', 'marketing'],
      default: 'functional'
    }
  },
  
  // Indexing and categorization
  tags: [{
    type: String,
    index: true
  }],
  
  category: {
    type: String,
    enum: ['security', 'performance', 'user_behavior', 'system', 'error'],
    default: 'user_behavior',
    index: true
  },
  
  // Privacy and retention
  isAnonymized: {
    type: Boolean,
    default: false,
    index: true
  },
  
  retentionExpiry: {
    type: Date,
    index: true // For automatic cleanup
  },
  
  // Audit trail
  createdAt: {
    type: Date,
    default: Date.now,
    index: true
  },
  
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  id: false,
  versionKey: false,
  // Optimize for time-series queries
  timeseries: {
    timeField: 'timestamp',
    metaField: 'userId',
    granularity: 'hours'
  }
});

// Compound indexes for efficient querying
UserActivityLogSchema.index({ userId: 1, timestamp: -1 });
UserActivityLogSchema.index({ action: 1, timestamp: -1 });
UserActivityLogSchema.index({ status: 1, timestamp: -1 });
UserActivityLogSchema.index({ resourceType: 1, resourceId: 1, timestamp: -1 });
UserActivityLogSchema.index({ category: 1, timestamp: -1 });
UserActivityLogSchema.index({ retentionExpiry: 1 }); // For cleanup jobs
UserActivityLogSchema.index({ 'requestMetadata.ipAddress': 1, timestamp: -1 });
UserActivityLogSchema.index({ tags: 1, timestamp: -1 });

// Text index for search functionality
UserActivityLogSchema.index({
  resourceName: 'text',
  resourcePath: 'text',
  'additionalData.searchQuery': 'text',
  'additionalData.errorMessage': 'text'
});

// Static methods for action types
UserActivityLogSchema.statics.ACTION_TYPES = ACTION_TYPES;
UserActivityLogSchema.statics.STATUS_TYPES = STATUS_TYPES;

// Instance methods
UserActivityLogSchema.methods.anonymize = function() {
  this.requestMetadata.ipAddress = 'anonymized';
  this.requestMetadata.userAgent = 'anonymized';
  this.sessionInfo.tokenId = 'anonymized';
  this.isAnonymized = true;
  this.updatedAt = new Date();
  return this.save();
};

// Pre-save middleware
UserActivityLogSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  
  // Set retention expiry if not set (default 1 year)
  if (!this.retentionExpiry) {
    const retentionPeriod = this.additionalData.dataRetentionCategory === 'essential' 
      ? 365 * 3 // 3 years for essential data
      : 365; // 1 year for other data
    
    this.retentionExpiry = new Date(Date.now() + retentionPeriod * 24 * 60 * 60 * 1000);
  }
  
  next();
});

module.exports = mongoConnections('master').model('UserActivityLog', UserActivityLogSchema);
