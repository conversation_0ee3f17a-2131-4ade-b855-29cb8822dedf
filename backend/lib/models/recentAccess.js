const mongoConnections = require("../connections/mongo");
const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const RecentAccessSchema = new mongoose.Schema(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      index: true
    },
    fileId: {
      type: Schema.Types.ObjectId,
      ref: 'File',
      required: true,
      index: true
    },
    fileName: {
      type: String,
      required: true
    },
    fileType: {
      type: String,
      required: true // image, video, audio, document, pdf, etc.
    },
    mimeType: {
      type: String,
      required: true
    },
    fileSize: {
      type: Number,
      required: true
    },
    accessedAt: {
      type: Date,
      default: Date.now,
      index: true
    },
    accessType: {
      type: String,
      enum: ['view', 'download', 'preview'],
      required: true,
      index: true
    },
    // Additional metadata for better UX
    thumbnailUrl: {
      type: String // For quick thumbnail display
    },
    parentFolderId: {
      type: Schema.Types.ObjectId,
      ref: 'Folder',
      default: null
    },
    // Track access frequency for potential future features
    accessCount: {
      type: Number,
      default: 1
    }
  },
  {
    id: false,
    versionKey: false,
    timestamps: false
  }
);

// Compound indexes for efficient queries
RecentAccessSchema.index({ userId: 1, accessedAt: -1 }); // Main query: get recent files by user
RecentAccessSchema.index({ userId: 1, fileId: 1 }); // For updating existing records
RecentAccessSchema.index({ userId: 1, accessType: 1, accessedAt: -1 }); // Filter by access type
RecentAccessSchema.index({ accessedAt: 1 }); // For cleanup of old records

// TTL index to automatically remove old records after 90 days
RecentAccessSchema.index({ accessedAt: 1 }, { expireAfterSeconds: 90 * 24 * 60 * 60 });

// Static methods
RecentAccessSchema.statics.recordAccess = async function(userId, fileData, accessType) {
  try {
    // Check if this file access already exists for this user
    const existingAccess = await this.findOne({
      userId: userId,
      fileId: fileData._id
    });

    if (existingAccess) {
      // Update existing record
      existingAccess.accessedAt = new Date();
      existingAccess.accessType = accessType;
      existingAccess.accessCount += 1;
      existingAccess.fileName = fileData.originalFileName; // Update in case file was renamed
      await existingAccess.save();
      return existingAccess;
    } else {
      // Create new record
      const newAccess = new this({
        userId: userId,
        fileId: fileData._id,
        fileName: fileData.originalFileName,
        fileType: getFileType(fileData.mimeType),
        mimeType: fileData.mimeType,
        fileSize: fileData.fileSize,
        accessType: accessType,
        parentFolderId: fileData.parentId
      });
      await newAccess.save();
      return newAccess;
    }
  } catch (error) {
    console.error('Error recording file access:', error);
    throw error;
  }
};

RecentAccessSchema.statics.getRecentAccess = async function(userId, limit = 20, offset = 0) {
  try {
    const recentFiles = await this.find({
      userId: userId
    })
    .sort({ accessedAt: -1 })
    .limit(limit)
    .skip(offset)
    .populate('fileId', 'originalFileName fileSize mimeType isDeleted parentId fileMetadata')
    .lean();

    // Filter out deleted files
    const validFiles = recentFiles.filter(access =>
      access.fileId && !access.fileId.isDeleted
    );

    return validFiles;
  } catch (error) {
    console.error('Error getting recent access:', error);
    throw error;
  }
};

RecentAccessSchema.statics.cleanupDeletedFiles = async function() {
  try {
    // Remove records for files that no longer exist or are deleted
    const FileModel = require('./file');
    const deletedFileIds = await FileModel.find({ isDeleted: true }).distinct('_id');

    if (deletedFileIds.length > 0) {
      await this.deleteMany({ fileId: { $in: deletedFileIds } });
    }

    return deletedFileIds.length;
  } catch (error) {
    console.error('Error cleaning up deleted files from recent access:', error);
    throw error;
  }
};

// Helper function to determine file type from MIME type
function getFileType(mimeType) {
  if (mimeType.startsWith('image/')) return 'image';
  if (mimeType.startsWith('video/')) return 'video';
  if (mimeType.startsWith('audio/')) return 'audio';
  if (mimeType === 'application/pdf') return 'pdf';
  if (mimeType.includes('document') || mimeType.includes('text') ||
      mimeType.includes('spreadsheet') || mimeType.includes('presentation')) return 'document';
  return 'other';
}

module.exports = mongoConnections("master").model("RecentAccess", RecentAccessSchema);
