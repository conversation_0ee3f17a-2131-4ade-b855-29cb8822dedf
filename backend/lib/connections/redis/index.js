const _ = require('lodash')
const config = require('../../config')
const redis = require('redis')

let connections = {}

class RedisConnection {
  constructor(name, options) {
    this.connection = null
    this.options = options
    this.name = name
    this.isConnected = false

    this.init()
  }

  async init() {
    try {
      // Create Redis client with v4+ API
      this.connection = redis.createClient({
        socket: {
          host: this.options.host,
          port: this.options.port
        },
        database: this.options.database || 0,
        password: this.options.password
      })

      this.connection.on('connect', () => {
        logger.logInfo(`[REDIS-${this.name}] - CONNECTING`)
      })

      this.connection.on('ready', () => {
        logger.logInfo(`[REDIS-${this.name}] - READY`)
        this.isConnected = true

        // Setup keepalive ping
        setInterval(async () => {
          try {
            if (this.isConnected) {
              await this.connection.ping()
            }
          } catch (err) {
            console.error('Redis keepalive error', err)
            this.isConnected = false
          }
        }, 30000)
      })

      this.connection.on('error', (err) => {
        logger.logError(`[REDIS-${this.name}]`, err)
        this.isConnected = false
      })

      this.connection.on('end', () => {
        logger.logInfo(`[REDIS-${this.name}] - DISCONNECTED`)
        this.isConnected = false
      })

      // Connect to Redis
      await this.connection.connect()

    } catch (error) {
      logger.logError(`[REDIS-${this.name}] - CONNECTION FAILED`, error)
      this.isConnected = false
    }
  }

  getConnection() {
    return this.connection
  }

  isReady() {
    return this.isConnected && this.connection && this.connection.isReady
  }
}

async function setUp() {
  const redisConfig = _.get(config, 'redis.connections', {})
  const connectionPromises = Object.keys(redisConfig).map(async (name) => {
    connections[name] = new RedisConnection(name, redisConfig[name])
    // Wait a bit for connection to initialize
    await new Promise(resolve => setTimeout(resolve, 100))
  })

  await Promise.all(connectionPromises)
}

// Initialize connections
setUp().catch(err => {
  console.error('Redis setup failed:', err)
})

module.exports = (name) => {
  return connections[name] || null
}
