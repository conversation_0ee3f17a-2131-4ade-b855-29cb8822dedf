const activityLoggerService = require('../services/activityLogger');
const UserActivityLogModel = require('../models/userActivityLog');
const _ = require('lodash');
const crypto = require('crypto');

/**
 * Activity logging middleware for Express.js
 * Automatically captures and logs user activities for relevant routes
 */
class ActivityLoggerMiddleware {
  constructor() {
    this.routeActionMap = this.buildRouteActionMap();
  }

  /**
   * Build mapping of routes to action types
   */
  buildRouteActionMap() {
    return {
      // Authentication routes
      'POST /auth/login': UserActivityLogModel.ACTION_TYPES.LOGIN,
      'POST /auth/register': UserActivityLogModel.ACTION_TYPES.REGISTER,
      'POST /auth/logout': UserActivityLogModel.ACTION_TYPES.LOGOUT,
      'POST /auth/change-password': UserActivityLogModel.ACTION_TYPES.PASSWORD_CHANGE,
      'POST /auth/reset-password': UserActivityLogModel.ACTION_TYPES.PASSWORD_RESET,
      'GET /auth/verify-email': UserActivityLogModel.ACTION_TYPES.EMAIL_VERIFICATION,
      'GET /auth/google/callback': UserActivityLogModel.ACTION_TYPES.OAUTH_LOGIN,
      'GET /auth/telegram/callback': UserActivityLogModel.ACTION_TYPES.OAUTH_LOGIN,
      
      // File operations
      'POST /files/upload': UserActivityLogModel.ACTION_TYPES.FILE_UPLOAD,
      'GET /files/download': UserActivityLogModel.ACTION_TYPES.FILE_DOWNLOAD,
      'GET /files/preview': UserActivityLogModel.ACTION_TYPES.FILE_PREVIEW,
      'GET /files/thumbnail': UserActivityLogModel.ACTION_TYPES.FILE_THUMBNAIL_VIEW,
      'GET /files/info': UserActivityLogModel.ACTION_TYPES.FILE_METADATA_VIEW,
      'GET /files/metadata': UserActivityLogModel.ACTION_TYPES.FILE_METADATA_VIEW,
      'DELETE /files': UserActivityLogModel.ACTION_TYPES.FILE_DELETE,
      
      // Folder operations
      'POST /folders/create': UserActivityLogModel.ACTION_TYPES.FOLDER_CREATE,
      'DELETE /folders': UserActivityLogModel.ACTION_TYPES.FOLDER_DELETE,
      'GET /browse': UserActivityLogModel.ACTION_TYPES.FOLDER_BROWSE,
      
      // Search operations
      'GET /search': UserActivityLogModel.ACTION_TYPES.SEARCH_QUERY,
      'POST /search': UserActivityLogModel.ACTION_TYPES.SEARCH_QUERY,
      
      // User profile
      'PUT /user/profile': UserActivityLogModel.ACTION_TYPES.PROFILE_UPDATE,
      'GET /user/storage': UserActivityLogModel.ACTION_TYPES.STORAGE_SYNC,
      
      // Admin operations
      'GET /admin/users': UserActivityLogModel.ACTION_TYPES.ADMIN_USER_VIEW,
      'GET /admin/logs': UserActivityLogModel.ACTION_TYPES.ADMIN_LOG_VIEW
    };
  }

  /**
   * Extract request metadata
   */
  extractRequestMetadata(req) {
    const startTime = Date.now();
    
    return {
      method: req.method,
      url: req.originalUrl || req.url,
      userAgent: req.get('User-Agent') || '',
      ipAddress: this.getClientIP(req),
      referer: req.get('Referer') || '',
      requestSize: this.calculateRequestSize(req),
      startTime
    };
  }

  /**
   * Get client IP address
   */
  getClientIP(req) {
    return req.ip || 
           req.connection?.remoteAddress || 
           req.socket?.remoteAddress || 
           (req.connection?.socket ? req.connection.socket.remoteAddress : null) ||
           '127.0.0.1';
  }

  /**
   * Calculate request size
   */
  calculateRequestSize(req) {
    let size = 0;
    
    // Headers size
    if (req.headers) {
      size += JSON.stringify(req.headers).length;
    }
    
    // Body size
    if (req.body) {
      size += JSON.stringify(req.body).length;
    }
    
    // Query parameters size
    if (req.query) {
      size += JSON.stringify(req.query).length;
    }
    
    return size;
  }

  /**
   * Extract session information
   */
  extractSessionInfo(req) {
    const sessionInfo = {};
    
    // Extract token information
    const token = _.get(req, 'headers.token', '') || _.get(req, 'query.token', '');
    if (token) {
      // Create a hash of the token for identification without storing the actual token
      sessionInfo.tokenId = crypto.createHash('sha256').update(token).digest('hex').substring(0, 16);
    }
    
    // Extract session ID if available
    if (req.session?.id) {
      sessionInfo.sessionId = req.session.id;
    }
    
    return sessionInfo;
  }

  /**
   * Determine action type from route
   */
  getActionType(req) {
    const routeKey = `${req.method} ${req.route?.path || req.path}`;
    
    // Try exact match first
    if (this.routeActionMap[routeKey]) {
      return this.routeActionMap[routeKey];
    }
    
    // Try pattern matching for parameterized routes
    for (const [pattern, action] of Object.entries(this.routeActionMap)) {
      const [method, path] = pattern.split(' ');
      if (req.method === method && this.matchesPattern(req.path, path)) {
        return action;
      }
    }
    
    return null;
  }

  /**
   * Check if path matches pattern (simple pattern matching)
   */
  matchesPattern(path, pattern) {
    // Convert pattern like "/files/:id" to regex
    const regexPattern = pattern.replace(/:[\w]+/g, '[^/]+');
    const regex = new RegExp(`^${regexPattern}$`);
    return regex.test(path);
  }

  /**
   * Extract resource information from request
   */
  extractResourceInfo(req, action) {
    const resourceInfo = {};
    
    // File operations
    if (action.includes('file')) {
      resourceInfo.resourceType = 'file';
      resourceInfo.resourceId = req.params?.fileId || req.body?.fileId;
      resourceInfo.resourceName = req.body?.originalFileName || req.file?.originalname;
    }
    
    // Folder operations
    if (action.includes('folder')) {
      resourceInfo.resourceType = 'folder';
      resourceInfo.resourceId = req.params?.folderId || req.body?.folderId;
      resourceInfo.resourceName = req.body?.folderName;
      resourceInfo.resourcePath = req.query?.path || req.body?.path;
    }
    
    // Search operations
    if (action.includes('search')) {
      resourceInfo.resourceType = 'search';
      resourceInfo.resourcePath = req.originalUrl;
    }
    
    // Auth operations
    if (action.includes('login') || action.includes('register') || action.includes('password')) {
      resourceInfo.resourceType = 'auth';
    }
    
    return resourceInfo;
  }

  /**
   * Extract additional data based on action type
   */
  extractAdditionalData(req, res, action) {
    const additionalData = {};
    
    // File-specific data
    if (action.includes('file')) {
      if (req.file) {
        additionalData.fileSize = req.file.size;
        additionalData.mimeType = req.file.mimetype;
        additionalData.fileExtension = req.file.originalname?.split('.').pop()?.toLowerCase();
      }
    }
    
    // Search-specific data
    if (action.includes('search')) {
      additionalData.searchQuery = req.query?.q || req.body?.query;
      additionalData.searchFilters = req.query?.filters || req.body?.filters;
    }
    
    // Auth-specific data
    if (action.includes('login') || action.includes('register')) {
      additionalData.authProvider = req.body?.provider || 'local';
      additionalData.loginMethod = req.body?.method || 'password';
    }
    
    // Response data
    if (res.locals?.responseData) {
      additionalData.responseData = res.locals.responseData;
    }
    
    return additionalData;
  }

  /**
   * Main middleware function
   */
  middleware() {
    return (req, res, next) => {
      // Skip if no user (for public routes)
      if (!req.user?.id) {
        return next();
      }
      
      const action = this.getActionType(req);
      
      // Skip if action is not tracked
      if (!action) {
        return next();
      }
      
      // Extract request metadata
      const requestMetadata = this.extractRequestMetadata(req);
      const sessionInfo = this.extractSessionInfo(req);
      const resourceInfo = this.extractResourceInfo(req, action);
      
      // Override res.json to capture response data
      const originalJson = res.json;
      res.json = function(data) {
        res.locals.responseData = data;
        return originalJson.call(this, data);
      };
      
      // Override res.end to log activity after response
      const originalEnd = res.end;
      res.end = function(...args) {
        // Calculate response time
        const responseTime = Date.now() - requestMetadata.startTime;
        
        // Determine status based on response
        let status = UserActivityLogModel.STATUS_TYPES.SUCCESS;
        if (res.statusCode >= 400) {
          status = res.statusCode >= 500 
            ? UserActivityLogModel.STATUS_TYPES.ERROR 
            : UserActivityLogModel.STATUS_TYPES.FAILURE;
        }
        
        // Extract additional data
        const additionalData = this.extractAdditionalData(req, res, action);
        
        // Log activity asynchronously
        setImmediate(() => {
          activityLoggerService.logActivity({
            userId: req.user.id,
            action,
            status,
            requestMetadata: {
              ...requestMetadata,
              responseTime,
              responseSize: args[0] ? Buffer.byteLength(args[0]) : 0
            },
            sessionInfo,
            ...resourceInfo,
            additionalData
          }).catch(error => {
            global.logger.logError('ActivityLoggerMiddleware: Error logging activity:', error.message);
          });
        });
        
        return originalEnd.apply(this, args);
      }.bind(this);
      
      next();
    };
  }

  /**
   * Middleware for specific route logging
   */
  logSpecificAction(action, options = {}) {
    return (req, res, next) => {
      if (!req.user?.id) {
        return next();
      }
      
      const requestMetadata = this.extractRequestMetadata(req);
      const sessionInfo = this.extractSessionInfo(req);
      
      // Log activity immediately for specific actions
      activityLoggerService.logActivity({
        userId: req.user.id,
        action,
        requestMetadata,
        sessionInfo,
        ...options
      }).catch(error => {
        global.logger.logError('ActivityLoggerMiddleware: Error logging specific action:', error.message);
      });
      
      next();
    };
  }
}

// Create singleton instance
const activityLoggerMiddleware = new ActivityLoggerMiddleware();

module.exports = {
  middleware: activityLoggerMiddleware.middleware.bind(activityLoggerMiddleware),
  logSpecificAction: activityLoggerMiddleware.logSpecificAction.bind(activityLoggerMiddleware)
};
