# Activity Log Cleanup and Rotation System

## Overview

The VisioBox activity log cleanup and rotation system provides automated and manual management of user activity logs to prevent database bloat while maintaining compliance and audit requirements.

## Features

- **Automated Cleanup**: Scheduled cleanup based on configurable retention policies
- **Category-based Retention**: Different retention periods for different types of activities
- **Archiving**: Optional archiving of logs before deletion
- **Manual Operations**: CLI and API tools for manual cleanup operations
- **Dry Run Mode**: Test cleanup operations without actually deleting data
- **Statistics**: Detailed statistics about log volumes and cleanup eligibility

## Configuration

### Environment Variables

Add these environment variables to configure the log cleanup system:

```bash
# Retention periods (in days)
ACTIVITY_LOGS_RETENTION_SECURITY=365          # Security events (login, logout, etc.)
ACTIVITY_LOGS_RETENTION_AUTHENTICATION=180    # Authentication events
ACTIVITY_LOGS_RETENTION_FILE_OPERATIONS=90    # File operations
ACTIVITY_LOGS_RETENTION_USER_BEHAVIOR=30      # Navigation, search, etc.
ACTIVITY_LOGS_RETENTION_SYSTEM=60             # System events
ACTIVITY_LOGS_RETENTION_ERRORS=90             # Error logs
ACTIVITY_LOGS_RETENTION_DEFAULT=30            # Default for uncategorized

# Cleanup scheduling
ACTIVITY_LOGS_CLEANUP_SCHEDULE="0 2 * * *"    # Daily at 2 AM UTC
ACTIVITY_LOGS_ARCHIVE_SCHEDULE="0 3 * * 0"    # Weekly on Sunday at 3 AM UTC
ACTIVITY_LOGS_CLEANUP_BATCH_SIZE=1000         # Batch size for operations

# Archiving
ACTIVITY_LOGS_ENABLE_ARCHIVING=false          # Enable/disable archiving
ACTIVITY_LOGS_ARCHIVE_PATH="./logs/archive"   # Archive storage path
```

### Default Retention Policies

| Category | Default Retention | Description |
|----------|------------------|-------------|
| Security | 365 days | Login, logout, password changes |
| Authentication | 180 days | Registration, email verification |
| File Operations | 90 days | Upload, download, delete, preview |
| User Behavior | 30 days | Navigation, search, browse |
| System | 60 days | Profile updates, settings changes |
| Errors | 90 days | Error logs and failures |
| Default | 30 days | Uncategorized activities |

## Usage

### Automatic Startup

The log cleanup service starts automatically when the application starts. Add this to your main application file:

```javascript
const { initializeLogCleanup } = require('./lib/startup/logCleanup');

// Initialize log cleanup service
initializeLogCleanup();
```

### CLI Operations

Use the CLI script for manual operations:

```bash
# Show cleanup statistics
node scripts/cleanup-logs.js --stats

# Perform cleanup (dry run)
node scripts/cleanup-logs.js --cleanup --dry-run

# Cleanup specific category
node scripts/cleanup-logs.js --cleanup --category=security --days=365

# Archive logs
node scripts/cleanup-logs.js --archive

# Show archive information
node scripts/cleanup-logs.js --archive-info

# Cleanup old archives
node scripts/cleanup-logs.js --cleanup-archives --archive-retention=365
```

### API Operations

Use the admin API for programmatic access:

```javascript
// Get cleanup statistics
POST /api/admin/logs/cleanup/v1.0
{
  "action": "stats"
}

// Perform manual cleanup
POST /api/admin/logs/cleanup/v1.0
{
  "action": "manual_cleanup",
  "category": "security",
  "olderThanDays": 365,
  "dryRun": true
}

// Archive logs
POST /api/admin/logs/cleanup/v1.0
{
  "action": "archive"
}

// Get archive information
POST /api/admin/logs/cleanup/v1.0
{
  "action": "archive_info"
}
```

## Service Management

### Starting the Service

```javascript
const logCleanupService = require('./lib/services/logCleanupService');
logCleanupService.start();
```

### Stopping the Service

```javascript
logCleanupService.stop();
```

### Manual Operations

```javascript
// Get statistics
const stats = await logCleanupService.getCleanupStats();

// Perform cleanup
const result = await logCleanupService.performCleanup();

// Manual cleanup with options
const manualResult = await logCleanupService.performManualCleanup({
  category: 'security',
  olderThanDays: 365,
  dryRun: true
});
```

## Archiving

When archiving is enabled, logs are saved to JSON files before deletion:

- Archive files are stored in the configured archive path
- Files are named with category, date, and timestamp
- Archives can be cleaned up based on retention policies
- Archive format is standard JSON for easy processing

### Archive File Format

```json
[
  {
    "_id": "log_id",
    "userId": "user_id",
    "actionType": "login",
    "timestamp": "2024-01-01T00:00:00.000Z",
    "status": "success",
    "resourceId": "resource_id",
    "resourceName": "resource_name",
    "requestMetadata": { ... },
    "sessionInfo": { ... },
    "additionalData": { ... }
  }
]
```

## Monitoring

### Log Output

The service provides detailed logging:

```
📋 Starting log cleanup service...
📋 Cleanup schedule: 0 2 * * *
📋 Archive schedule: 0 3 * * 0
📋 Running scheduled log cleanup...
📋 Cleaning security logs older than 365 days
📋 Deleted 150 security log entries
📋 Log cleanup completed: 500 entries deleted in 1250ms
```

### Statistics

Get detailed statistics about log volumes:

```javascript
{
  "security": {
    "total": 1000,
    "eligibleForCleanup": 150,
    "retentionDays": 365,
    "cutoffDate": "2023-01-01T00:00:00.000Z"
  },
  "fileOperations": {
    "total": 5000,
    "eligibleForCleanup": 2000,
    "retentionDays": 90,
    "cutoffDate": "2023-10-01T00:00:00.000Z"
  }
}
```

## Best Practices

1. **Regular Monitoring**: Check cleanup statistics regularly
2. **Test First**: Use dry run mode before performing large cleanups
3. **Archive Important Data**: Enable archiving for compliance requirements
4. **Adjust Retention**: Tune retention periods based on your needs
5. **Monitor Performance**: Watch for performance impact during cleanup
6. **Backup Archives**: Backup archive files to external storage

## Troubleshooting

### Common Issues

1. **Service Won't Start**: Check configuration and permissions
2. **Cleanup Fails**: Check database connectivity and permissions
3. **Archive Errors**: Verify archive path exists and is writable
4. **Performance Issues**: Adjust batch size and schedule

### Error Handling

The service includes comprehensive error handling:

- Failed operations are logged with details
- Service continues running even if individual operations fail
- Graceful shutdown on application termination
- Automatic retry for transient failures

## Security Considerations

- Admin API requires proper authentication
- Archive files may contain sensitive data
- Consider encryption for archived data
- Implement proper access controls for archive storage
- Regular security audits of retention policies
