#!/usr/bin/env node

/**
 * Test script to debug hash calculation differences
 * Usage: node test-hash-debug.js <file1> <file2>
 */

const fs = require('fs');
const crypto = require('crypto');
const path = require('path');

// Import our metadata utility
const metadataUtil = require('./lib/util/metadata');

async function calculateBasicHash(filePath) {
  return new Promise((resolve, reject) => {
    const md5Hash = crypto.createHash('md5');
    const sha256Hash = crypto.createHash('sha256');
    
    const stream = fs.createReadStream(filePath);
    
    stream.on('data', (data) => {
      md5Hash.update(data);
      sha256Hash.update(data);
    });
    
    stream.on('end', () => {
      resolve({
        md5: md5Hash.digest('hex'),
        sha256: sha256Hash.digest('hex')
      });
    });
    
    stream.on('error', reject);
  });
}

async function analyzeFile(filePath) {
  console.log(`\n=== Analyzing: ${filePath} ===`);
  
  if (!fs.existsSync(filePath)) {
    console.log('❌ File does not exist');
    return null;
  }
  
  const stats = fs.statSync(filePath);
  console.log(`📊 File size: ${stats.size} bytes`);
  console.log(`📅 Modified: ${stats.mtime.toISOString()}`);
  console.log(`📅 Created: ${stats.ctime.toISOString()}`);
  
  // Calculate hash using basic method
  console.log('\n🔍 Basic hash calculation...');
  const basicHash = await calculateBasicHash(filePath);
  console.log(`MD5: ${basicHash.md5}`);
  console.log(`SHA256: ${basicHash.sha256}`);
  
  // Calculate hash using our enhanced method
  console.log('\n🔍 Enhanced hash calculation...');
  const enhancedHash = await metadataUtil.calculateFileHashes(filePath);
  console.log(`MD5: ${enhancedHash.md5}`);
  console.log(`SHA256: ${enhancedHash.sha256}`);
  console.log(`Debug info:`, enhancedHash.debug);
  
  // Compare results
  const md5Match = basicHash.md5 === enhancedHash.md5;
  const sha256Match = basicHash.sha256 === enhancedHash.sha256;
  
  console.log(`\n✅ Hash comparison:`);
  console.log(`MD5 match: ${md5Match ? '✅' : '❌'}`);
  console.log(`SHA256 match: ${sha256Match ? '✅' : '❌'}`);
  
  return {
    filePath,
    stats,
    basicHash,
    enhancedHash,
    matches: { md5Match, sha256Match }
  };
}

async function compareFiles(file1Path, file2Path) {
  console.log('\n🔄 Comparing two files...');
  
  const file1Analysis = await analyzeFile(file1Path);
  const file2Analysis = await analyzeFile(file2Path);
  
  if (!file1Analysis || !file2Analysis) {
    console.log('❌ Cannot compare - one or both files missing');
    return;
  }
  
  console.log('\n📊 File Comparison Summary:');
  console.log(`File 1: ${path.basename(file1Path)} (${file1Analysis.stats.size} bytes)`);
  console.log(`File 2: ${path.basename(file2Path)} (${file2Analysis.stats.size} bytes)`);
  
  const sizeMatch = file1Analysis.stats.size === file2Analysis.stats.size;
  console.log(`Size match: ${sizeMatch ? '✅' : '❌'}`);
  
  // Compare hashes using our utility function
  const hashComparison = metadataUtil.compareFileHashes(
    file1Analysis.enhancedHash,
    file2Analysis.enhancedHash
  );
  
  console.log('\n🔍 Hash Comparison:');
  console.log(`Files identical: ${hashComparison.isIdentical ? '✅' : '❌'}`);
  console.log(`MD5 match: ${hashComparison.md5Match ? '✅' : '❌'}`);
  console.log(`SHA256 match: ${hashComparison.sha256Match ? '✅' : '❌'}`);
  console.log(`Analysis: ${hashComparison.analysis.join(', ')}`);
  
  if (!hashComparison.isIdentical) {
    console.log('\n🔍 Detailed Hash Differences:');
    console.log(`File 1 MD5:    ${file1Analysis.enhancedHash.md5}`);
    console.log(`File 2 MD5:    ${file2Analysis.enhancedHash.md5}`);
    console.log(`File 1 SHA256: ${file1Analysis.enhancedHash.sha256}`);
    console.log(`File 2 SHA256: ${file2Analysis.enhancedHash.sha256}`);
  }
}

async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('Usage: node test-hash-debug.js <file1> [file2]');
    console.log('');
    console.log('Examples:');
    console.log('  node test-hash-debug.js ./test-file.jpg');
    console.log('  node test-hash-debug.js ./file1.jpg ./file2.jpg');
    process.exit(1);
  }
  
  if (args.length === 1) {
    // Analyze single file
    await analyzeFile(args[0]);
  } else {
    // Compare two files
    await compareFiles(args[0], args[1]);
  }
}

main().catch(console.error);
