const { expect } = require('chai');
const sinon = require('sinon');
const fs = require('fs').promises;
const path = require('path');
const logCleanupService = require('../../lib/services/logCleanupService');
const UserActivityLogModel = require('../../lib/models/userActivityLog');

describe('LogCleanupService', () => {
  let sandbox;
  
  beforeEach(() => {
    sandbox = sinon.createSandbox();
  });
  
  afterEach(() => {
    sandbox.restore();
  });

  describe('getRetentionPeriod', () => {
    it('should return correct retention period for security actions', () => {
      const period = logCleanupService.getRetentionPeriod(UserActivityLogModel.ACTION_TYPES.LOGIN);
      expect(period).to.equal(365); // Default security retention
    });

    it('should return correct retention period for file operations', () => {
      const period = logCleanupService.getRetentionPeriod(UserActivityLogModel.ACTION_TYPES.FILE_UPLOAD);
      expect(period).to.equal(90); // Default file operations retention
    });

    it('should return default retention for unknown actions', () => {
      const period = logCleanupService.getRetentionPeriod('unknown_action');
      expect(period).to.equal(30); // Default retention
    });
  });

  describe('getCleanupStats', () => {
    beforeEach(() => {
      sandbox.stub(UserActivityLogModel, 'countDocuments');
    });

    it('should return cleanup statistics for all categories', async () => {
      UserActivityLogModel.countDocuments.resolves(100);
      
      const stats = await logCleanupService.getCleanupStats();
      
      expect(stats).to.have.property('security');
      expect(stats).to.have.property('fileOperations');
      expect(stats).to.have.property('userBehavior');
      expect(stats).to.have.property('system');
      expect(stats).to.have.property('uncategorized');
      
      expect(stats.security).to.have.property('total');
      expect(stats.security).to.have.property('eligibleForCleanup');
      expect(stats.security).to.have.property('retentionDays');
      expect(stats.security).to.have.property('cutoffDate');
    });
  });

  describe('performManualCleanup', () => {
    beforeEach(() => {
      sandbox.stub(UserActivityLogModel, 'deleteMany');
      sandbox.stub(UserActivityLogModel, 'countDocuments');
    });

    it('should perform dry run without deleting logs', async () => {
      UserActivityLogModel.countDocuments.resolves(50);
      
      const result = await logCleanupService.performManualCleanup({
        category: 'security',
        olderThanDays: 30,
        dryRun: true
      });
      
      expect(result.success).to.be.true;
      expect(result.dryRun).to.be.true;
      expect(result.wouldDelete).to.equal(50);
      expect(UserActivityLogModel.deleteMany.called).to.be.false;
    });

    it('should delete logs when not in dry run mode', async () => {
      UserActivityLogModel.deleteMany.resolves({ deletedCount: 25 });
      
      const result = await logCleanupService.performManualCleanup({
        category: 'security',
        olderThanDays: 30,
        dryRun: false
      });
      
      expect(result.success).to.be.true;
      expect(result.dryRun).to.be.false;
      expect(result.deletedCount).to.equal(25);
      expect(UserActivityLogModel.deleteMany.calledOnce).to.be.true;
    });

    it('should handle invalid category', async () => {
      const result = await logCleanupService.performManualCleanup({
        category: 'invalid_category'
      });
      
      expect(result.success).to.be.false;
      expect(result.error).to.include('Invalid category');
    });
  });

  describe('performCleanup', () => {
    beforeEach(() => {
      sandbox.stub(logCleanupService, 'cleanupByCategory').resolves(10);
      sandbox.stub(logCleanupService, 'cleanupUncategorized').resolves(5);
    });

    it('should perform cleanup for all categories', async () => {
      const result = await logCleanupService.performCleanup();
      
      expect(result.success).to.be.true;
      expect(result.deletedCount).to.be.greaterThan(0);
      expect(result.duration).to.be.a('number');
    });
  });

  describe('archiving functionality', () => {
    beforeEach(() => {
      sandbox.stub(fs, 'access').resolves();
      sandbox.stub(fs, 'mkdir').resolves();
      sandbox.stub(fs, 'writeFile').resolves();
      sandbox.stub(fs, 'readdir').resolves(['test1.json', 'test2.json']);
      sandbox.stub(fs, 'stat').resolves({
        size: 1024,
        birthtime: new Date(),
        mtime: new Date()
      });
      sandbox.stub(UserActivityLogModel, 'find').resolves([
        { _id: '1', actionType: 'login', timestamp: new Date() },
        { _id: '2', actionType: 'logout', timestamp: new Date() }
      ]);
    });

    it('should create archive directory if it does not exist', async () => {
      fs.access.rejects(new Error('Directory not found'));
      
      await logCleanupService.ensureArchiveDirectory();
      
      expect(fs.mkdir.calledOnce).to.be.true;
    });

    it('should archive logs by category', async () => {
      const archived = await logCleanupService.archiveByCategory(
        'security',
        [UserActivityLogModel.ACTION_TYPES.LOGIN],
        new Date()
      );
      
      expect(archived).to.equal(2);
      expect(fs.writeFile.calledOnce).to.be.true;
    });

    it('should get archive information', async () => {
      const info = await logCleanupService.getArchiveInfo();
      
      expect(info).to.have.property('archivePath');
      expect(info).to.have.property('totalFiles');
      expect(info).to.have.property('files');
      expect(info.files).to.be.an('array');
    });
  });

  describe('service lifecycle', () => {
    it('should start and stop the service', () => {
      expect(logCleanupService.isRunning).to.be.false;
      
      logCleanupService.start();
      expect(logCleanupService.isRunning).to.be.true;
      
      logCleanupService.stop();
      expect(logCleanupService.isRunning).to.be.false;
    });

    it('should not start if already running', () => {
      logCleanupService.start();
      const consoleSpy = sandbox.spy(console, 'log');
      
      logCleanupService.start(); // Try to start again
      
      expect(consoleSpy.calledWith('📋 Log cleanup service is already running')).to.be.true;
      
      logCleanupService.stop();
    });
  });
});
