{"name": "nodejs-backend-template", "version": "1.0.0", "description": "A clean Node.js backend template with MongoDB and Redis", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node index.js", "dev": "nodemon index.js", "validate-config": "node scripts/validate-config.js", "setup": "npm run validate-config && echo '✅ Configuration validated. Ready to start!'"}, "license": "MIT", "dependencies": {"async": "^3.2.4", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "config": "^3.3.9", "cors": "^2.8.5", "dotenv": "^17.2.1", "exifr": "^7.1.3", "express": "^4.18.2", "express-session": "^1.17.3", "geoip-lite": "^1.4.10", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "moment": "^2.29.4", "mongoose": "^7.5.0", "ms": "^2.1.3", "multer": "^2.0.1", "node-cron": "^4.2.1", "node-fetch": "^2.7.0", "node-telegram-bot-api": "^0.66.0", "nodemailer": "^6.10.1", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-telegram": "^1.0.0", "redis": "^4.6.8", "sharp": "^0.34.3", "socket.io": "^4.7.2", "ua-parser-js": "^2.0.4", "uuid": "^11.1.0", "winston": "^3.10.0", "winston-daily-rotate-file": "^4.7.1"}, "devDependencies": {"nodemon": "^3.0.1"}}