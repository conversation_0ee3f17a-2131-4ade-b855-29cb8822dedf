# VisioBox Backend Configuration

# Server Configuration
PORT=3000
NODE_ENV=development
SECRET_KEY=your-secret-key-here
SERVICE_NAME=VisioBox

# MongoDB Configuration
MONGO_HOST=localhost
MONGO_PORT=27017
MONGO_DATABASE=visiobox
MONGO_USER=
MONGO_PASSWORD=

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DATABASE=0
REDIS_PASSWORD=

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your-telegram-bot-token
TELEGRAM_CHAT_ID=your-telegram-chat-id
TELEGRAM_ADMIN_CHAT_ID=your-admin-chat-id

# OAuth Configuration
OAUTH_ENABLED=false
OAUTH_GOOGLE_ENABLED=false
OAUTH_GOOGLE_CLIENT_ID=
OAUTH_GOOGLE_CLIENT_SECRET=
OAUTH_GOOGLE_CALLBACK_URL=http://localhost:3000/api/auth/oauth/google/callback

OAUTH_TELEGRAM_ENABLED=false
OAUTH_TELEGRAM_BOT_TOKEN=
OAUTH_TELEGRAM_CALLBACK_URL=http://localhost:3000/api/auth/oauth/telegram/callback

# Session Configuration
SESSION_SECRET=your-session-secret
SESSION_MAX_AGE=86400000

# CORS Configuration
CORS_ORIGIN=http://localhost:3001
CORS_CREDENTIALS=true

# Email Configuration
EMAIL_ENABLED=false
EMAIL_SMTP_HOST=
EMAIL_SMTP_PORT=587
EMAIL_SMTP_SECURE=false
EMAIL_SMTP_USER=
EMAIL_SMTP_PASS=
EMAIL_FROM=<EMAIL>

# Frontend Configuration
FRONTEND_URL=http://localhost:3001

# Upload Configuration
UPLOADS_MAX_FILE_SIZE=100MB
UPLOADS_ALLOWED_TYPES=*

# Activity Logs Configuration
# Retention periods in days
ACTIVITY_LOGS_RETENTION_SECURITY=365
ACTIVITY_LOGS_RETENTION_AUTHENTICATION=180
ACTIVITY_LOGS_RETENTION_FILE_OPERATIONS=90
ACTIVITY_LOGS_RETENTION_USER_BEHAVIOR=30
ACTIVITY_LOGS_RETENTION_SYSTEM=60
ACTIVITY_LOGS_RETENTION_ERRORS=90
ACTIVITY_LOGS_RETENTION_DEFAULT=30

# Cleanup scheduling (cron format)
ACTIVITY_LOGS_CLEANUP_SCHEDULE="0 2 * * *"
ACTIVITY_LOGS_ARCHIVE_SCHEDULE="0 3 * * 0"
ACTIVITY_LOGS_CLEANUP_BATCH_SIZE=1000

# Archiving configuration
ACTIVITY_LOGS_ENABLE_ARCHIVING=false
ACTIVITY_LOGS_ARCHIVE_PATH=./logs/archive
