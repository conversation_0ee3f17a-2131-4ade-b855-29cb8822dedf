version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    env_file:
      - .env.production
    environment:
      # Override for Docker containers
      - NODE_ENV=production
      - MONGO_HOST=mongodb
      - REDIS_HOST=redis
      - PORT=3001
    depends_on:
      - mongodb
      - redis
    volumes:
      - ./backend/logs:/app/logs
      - ./backend/public/uploads:/app/public/uploads
    networks:
      - app_network
    restart: unless-stopped

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "80:80"
    environment:
      - REACT_APP_API_URL=http://localhost:3001
    depends_on:
      - backend
    networks:
      - app_network
    restart: unless-stopped

  mongodb:
    image: mongo:7.0
    ports:
      - "27018:27017"
    environment:
      - MONGO_INITDB_DATABASE=visiobox
    volumes:
      - mongodb_data:/data/db
      - ./mongodb-init:/docker-entrypoint-initdb.d
    networks:
      - app_network
    restart: unless-stopped

  redis:
    image: redis:7.2-alpine
    ports:
      - "6380:6379"
    volumes:
      - redis_data:/data
    networks:
      - app_network
    restart: unless-stopped
    command: redis-server --appendonly yes

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local

networks:
  app_network:
    driver: bridge
