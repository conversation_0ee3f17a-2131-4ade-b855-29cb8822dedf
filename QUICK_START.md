# VisiBox Quick Start

## 🚀 Setup in 3 Steps

### 1. Copy and Configure Environment
```bash
# Copy the example file
cp .env.example .env

# Edit with your values
nano .env
```

### 2. Set Required Values
Edit `.env` and add these **required** values:
```env
SECRET_KEY=your_32_character_secret_key_here
SESSION_SECRET=your_session_secret_here
TELEGRAM_BOT_TOKEN=your_bot_token_from_botfather
TELEGRAM_CHAT_ID=your_telegram_chat_id
```

### 3. Validate and Start
```bash
# Validate configuration
cd backend
npm run validate-config

# Start the application
npm run dev
```

## 🔧 Adding Email Accounts (Optional)

Add as many as you need:
```env
EMAIL_SERVICE_1=gmail
EMAIL_USER_1=<EMAIL>
EMAIL_PASS_1=app_password_1

EMAIL_SERVICE_2=gmail
EMAIL_USER_2=<EMAIL>
EMAIL_PASS_2=app_password_2

# Continue with _3, _4, etc.
```

## 📧 Email Alerts (Optional)
```env
EMAIL_ALERTS=<EMAIL>,<EMAIL>
```

## ✅ Benefits of New Configuration

- **🔒 Secure**: No sensitive data in code
- **🔄 Flexible**: Dynamic number of email accounts
- **🚫 Safe**: .env file ignored by git
- **⚡ Easy**: Simple environment variable setup

## 🆘 Need Help?

- **Detailed guide**: See `CONFIG_SETUP.md`
- **Validation errors**: Run `npm run validate-config`
- **Missing .env**: Copy from `.env.example`

## 🎯 What Changed?

### Before
```javascript
// ❌ Hardcoded secrets in config
emailInfos: [
  { user: '<EMAIL>', pass: 'hardcoded_password' }
]
```

### After
```javascript
// ✅ Dynamic from environment
emailInfos: buildEmailConfigs() // Reads EMAIL_USER_1, EMAIL_USER_2, etc.
```

Now you can safely commit your configuration code! 🎉
