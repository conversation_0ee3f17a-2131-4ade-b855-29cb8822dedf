<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Main Box Container -->
  <defs>
    <linearGradient id="boxGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2563EB;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#60A5FA;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="lensGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F8FAFC;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 3D Box Base -->
  <path d="M80 120 L432 120 L480 160 L480 400 L128 400 L80 360 Z" fill="url(#boxGradient)" opacity="0.9"/>
  
  <!-- 3D Box Top -->
  <path d="M80 120 L432 120 L480 80 L128 80 Z" fill="url(#boxGradient)"/>
  
  <!-- 3D Box Side -->
  <path d="M432 120 L480 80 L480 160 L432 200 Z" fill="url(#boxGradient)" opacity="0.7"/>
  
  <!-- Main Box Front -->
  <rect x="80" y="120" width="352" height="280" rx="16" fill="url(#boxGradient)"/>
  
  <!-- Camera Lens Circle -->
  <circle cx="256" cy="260" r="80" fill="url(#lensGradient)" stroke="#1E40AF" stroke-width="4"/>
  
  <!-- Lens Inner Ring -->
  <circle cx="256" cy="260" r="60" fill="none" stroke="#2563EB" stroke-width="2"/>
  
  <!-- Aperture Blades -->
  <g transform="translate(256,260)">
    <path d="M-30 0 L-15 -26 L15 -26 L30 0 L15 26 L-15 26 Z" fill="#1E40AF" opacity="0.3"/>
    <path d="M-21 -21 L0 -30 L21 -21 L21 21 L0 30 L-21 21 Z" fill="#2563EB" opacity="0.2"/>
  </g>
  
  <!-- Central Dot -->
  <circle cx="256" cy="260" r="8" fill="#1E40AF"/>
  
  <!-- Handle -->
  <rect x="440" y="240" width="32" height="40" rx="16" fill="url(#lensGradient)" stroke="#2563EB" stroke-width="2"/>
  <circle cx="456" cy="260" r="6" fill="#2563EB"/>
  
  <!-- Highlight Effects -->
  <ellipse cx="200" cy="180" rx="40" ry="20" fill="#FFFFFF" opacity="0.3"/>
  <ellipse cx="320" cy="200" rx="20" ry="10" fill="#FFFFFF" opacity="0.2"/>
</svg>
