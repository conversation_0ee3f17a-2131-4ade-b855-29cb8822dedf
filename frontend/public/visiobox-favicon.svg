<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="boxGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2563EB;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#60A5FA;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Main Box -->
  <rect x="2" y="6" width="28" height="20" rx="2" fill="url(#boxGradient)"/>
  
  <!-- Camera Lens -->
  <circle cx="16" cy="16" r="8" fill="white" stroke="#1E40AF" stroke-width="1"/>
  
  <!-- Inner Ring -->
  <circle cx="16" cy="16" r="6" fill="none" stroke="#2563EB" stroke-width="0.5"/>
  
  <!-- Center Dot -->
  <circle cx="16" cy="16" r="2" fill="#1E40AF"/>
  
  <!-- Handle -->
  <rect x="26" y="14" width="4" height="4" rx="2" fill="white" stroke="#2563EB" stroke-width="0.5"/>
</svg>
