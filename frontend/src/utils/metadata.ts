/**
 * Client-side metadata collection utilities
 */

export interface ClientMetadata {
  // Device/Browser info
  screenResolution?: string;
  timezone?: string;
  language?: string;

  // Network info
  connectionType?: string;
  downlink?: number;
  effectiveType?: string;
  rtt?: number;

  // Upload info
  uploadSource?: string;
  retryCount?: number;
}

/**
 * Get user's geolocation (DISABLED - not needed anymore)
 */
export const getGeolocation = (): Promise<GeolocationPosition | null> => {
  // Disabled - we get location from EXIF data instead of bothering users
  return Promise.resolve(null);
};

/**
 * Get device and browser information
 */
export const getDeviceInfo = (): Partial<ClientMetadata> => {
  const metadata: Partial<ClientMetadata> = {};

  // Screen resolution
  if (window.screen?.width && window.screen?.height) {
    metadata.screenResolution = `${window.screen.width}x${window.screen.height}`;
  }

  // Timezone
  try {
    metadata.timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  } catch (error) {
    console.warn('Error getting timezone:', error);
  }

  // Language
  metadata.language = navigator.language || navigator.languages?.[0];

  return metadata;
};

/**
 * Get network information if available
 */
export const getNetworkInfo = (): Partial<ClientMetadata> => {
  const metadata: Partial<ClientMetadata> = {};

  // Check if Network Information API is available
  const connection = (navigator as any).connection ||
                    (navigator as any).mozConnection ||
                    (navigator as any).webkitConnection;

  if (connection) {
    metadata.connectionType = connection.type || connection.effectiveType;
    metadata.downlink = connection.downlink;
    metadata.effectiveType = connection.effectiveType;
    metadata.rtt = connection.rtt;
  }

  return metadata;
};

/**
 * Detect upload source based on how the file was added
 */
export const detectUploadSource = (event?: Event): string => {
  if (!event) return 'web';

  // Check if it's from drag and drop
  if (event.type === 'drop') {
    return 'drag-drop';
  }

  // Check if it's from paste
  if (event.type === 'paste') {
    return 'paste';
  }

  // Check if it's from mobile (touch events)
  if ('touches' in event) {
    return 'mobile';
  }

  return 'web';
};

/**
 * Collect comprehensive client-side metadata
 */
export const collectClientMetadata = async (
  uploadSource?: string,
  retryCount: number = 0
): Promise<ClientMetadata> => {
  const metadata: ClientMetadata = {
    retryCount,
    uploadSource: uploadSource || 'web'
  };

  // Get device info (synchronous)
  Object.assign(metadata, getDeviceInfo());

  // Get network info (synchronous)
  Object.assign(metadata, getNetworkInfo());

  return metadata;
};

/**
 * Request geolocation permission (DISABLED - not needed anymore)
 */
export const requestGeolocationPermission = (): Promise<boolean> => {
  // Disabled - we get location from EXIF data instead of bothering users
  return Promise.resolve(false);
};

/**
 * Format metadata for debugging
 */
export const formatMetadataForDebug = (metadata: ClientMetadata): string => {
  const parts = [];

  if (metadata.screenResolution) parts.push(`Screen: ${metadata.screenResolution}`);
  if (metadata.timezone) parts.push(`TZ: ${metadata.timezone}`);
  if (metadata.language) parts.push(`Lang: ${metadata.language}`);
  if (metadata.connectionType) parts.push(`Net: ${metadata.connectionType}`);
  if (metadata.uploadSource) parts.push(`Source: ${metadata.uploadSource}`);

  return parts.join(' | ');
};
