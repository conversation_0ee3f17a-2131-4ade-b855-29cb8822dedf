import { fileApi } from '../services/api';
import { downloadFile, getFileCategory } from './helpers';

export interface PreviewableFile {
  id: string;
  name: string;
  mimeType: string;
  size: number;
}

export const getPreviewUrl = (fileId: string): string => {
  return fileApi.previewFile(fileId);
};

export const handleFileDownload = (file: PreviewableFile): void => {
  const downloadUrl = fileApi.downloadFile(file.id);
  downloadFile(downloadUrl, file.name);
};

export const getThumbnailUrl = (file: PreviewableFile): string | null => {
  if (file.mimeType.startsWith('image/')) {
    return fileApi.thumbnailFile(file.id);
  }
  return null;
};

export const isPreviewableFile = (mimeType: string): boolean => {
  const category = getFileCategory(mimeType);
  return ['image', 'video', 'audio', 'text', 'office'].includes(category);
};

// Helper functions for preview content - to be used in React components
export const getFilePreviewCategory = (mimeType: string): string => {
  return getFileCategory(mimeType);
};

export const getPreviewContentProps = (file: PreviewableFile) => {
  return {
    previewUrl: getPreviewUrl(file.id),
    thumbnailUrl: getThumbnailUrl(file),
    fileName: file.name,
    fileSize: file.size,
    mimeType: file.mimeType,
    category: getFilePreviewCategory(file.mimeType)
  };
};
