import React, { useEffect } from 'react';
import { Snackbar, Alert, useTheme } from '@mui/material';
import { useAlert } from '../contexts/AlertContext';
import { alertService } from '../services/alertService';

const AlertContainer: React.FC = () => {
  const { state, dispatch } = useAlert();
  const theme = useTheme();

  // Khởi tạo alertService với dispatch function
  useEffect(() => {
    alertService.init(dispatch);
  }, [dispatch]);

  const handleClose = (alertId: string) => {
    alertService.removeAlert(alertId);
  };

  return (
    <>
      {state.alerts.map((alert, index) => (
        <Snackbar
          key={alert.id}
          open={true}
          autoHideDuration={null} // Chúng ta tự quản lý thời gian trong service
          onClose={() => handleClose(alert.id)}
          anchorOrigin={{ 
            vertical: 'bottom', 
            horizontal: 'left' 
          }}
          sx={{ 
            mb: 8 + (index * 7), // Stack alerts vertically với khoảng cách
            zIndex: 1400 + index, // Đảm bảo alerts mới nhất ở trên
          }}
        >
          <Alert
            onClose={() => handleClose(alert.id)}
            severity={alert.severity}
            variant="filled"
            sx={{
              width: '100%',
              borderRadius: 2,
              boxShadow: theme.palette.mode === 'dark'
                ? '0 4px 20px rgba(0, 0, 0, 0.4)'
                : '0 4px 20px rgba(148, 163, 184, 0.1)',
              // Thêm animation cho smooth appearance
              animation: 'slideInLeft 0.3s ease-out',
              '@keyframes slideInLeft': {
                '0%': {
                  transform: 'translateX(-100%)',
                  opacity: 0,
                },
                '100%': {
                  transform: 'translateX(0)',
                  opacity: 1,
                },
              },
            }}
          >
            {alert.message}
          </Alert>
        </Snackbar>
      ))}
    </>
  );
};

export default AlertContainer;
