import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>pography,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  LinearProgress,
  useTheme,
  useMediaQuery,
  Paper,
  Divider,
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import {
  DeleteForever as DeleteForeverIcon,
  Info as InfoIcon,
} from '@mui/icons-material';
import { Item } from '../types';
import { fileApi } from '../services/api';
import { formatFileSize } from '../utils/helpers';
import { alertService } from '../services/alertService';
import { getErrorMessage } from '../utils/errorHandler';
import TrashGrid from './TrashGrid';

interface TrashSummary {
  totalItems: number;
  totalFiles: number;
  totalFolders: number;
  totalSize: number;
}

const TrashPage: React.FC = () => {
  const theme = useTheme();
  const { t } = useTranslation();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [items, setItems] = useState<Item[]>([]);
  const [summary, setSummary] = useState<TrashSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [emptyTrashDialogOpen, setEmptyTrashDialogOpen] = useState(false);
  const [emptyingTrash, setEmptyingTrash] = useState(false);

  const loadTrashItems = async () => {
    try {
      setLoading(true);
      const response = await fileApi.getTrashItems();
      if (response.data) {
        setItems(response.data.items || []);
        setSummary(response.data.summary || null);
      }
    } catch (error: any) {
      alertService.error(getErrorMessage(error, 'Failed to load trash items'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadTrashItems();
  }, []);

  const handleEmptyTrash = async () => {
    try {
      setEmptyingTrash(true);
      await fileApi.emptyTrash();
      alertService.success(t('trash.emptySuccess', 'Trash emptied successfully'));
      setItems([]);
      setSummary(null);
      setEmptyTrashDialogOpen(false);
    } catch (error: any) {
      alertService.error(getErrorMessage(error, 'Failed to empty trash'));
    } finally {
      setEmptyingTrash(false);
    }
  };

  const handleItemRestore = (item: Item) => {
    setItems(prevItems => prevItems.filter(i => i.id !== item.id));
    if (summary) {
      setSummary(prev => prev ? {
        ...prev,
        totalItems: prev.totalItems - 1,
        totalFiles: item.type === 'file' ? prev.totalFiles - 1 : prev.totalFiles,
        totalFolders: item.type === 'folder' ? prev.totalFolders - 1 : prev.totalFolders,
        totalSize: item.type === 'file' ? prev.totalSize - (item as any).size : prev.totalSize,
      } : null);
    }
  };

  const handleItemPermanentDelete = (item: Item) => {
    setItems(prevItems => prevItems.filter(i => i.id !== item.id));
    if (summary) {
      setSummary(prev => prev ? {
        ...prev,
        totalItems: prev.totalItems - 1,
        totalFiles: item.type === 'file' ? prev.totalFiles - 1 : prev.totalFiles,
        totalFolders: item.type === 'folder' ? prev.totalFolders - 1 : prev.totalFolders,
        totalSize: item.type === 'file' ? prev.totalSize - (item as any).size : prev.totalSize,
      } : null);
    }
  };

  return (
    <Box sx={{ p: isMobile ? 2 : 3 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <DeleteForeverIcon sx={{
            mr: 2,
            fontSize: 40,
            color: 'text.secondary',
            p: 1,
            backgroundColor: 'grey.100',
            borderRadius: '50%'
          }} />
          <Box>
            <Typography variant="h4" sx={{ fontWeight: 700, mb: 0.5 }}>
              {t('trash.title', 'Trash')}
            </Typography>
            <Typography variant="body1" color="text.secondary">
              {t('trash.description', 'Items in trash will be automatically deleted after 30 days')}
            </Typography>
          </Box>
        </Box>

        {summary && summary.totalItems > 0 && (
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 2,
            p: 2,
            backgroundColor: 'warning.light',
            borderRadius: 2,
            border: '1px solid',
            borderColor: 'warning.main'
          }}>
            <InfoIcon sx={{ color: 'text.primary' }} />
            <Typography variant="body2" sx={{ color: 'text.primary' }}>
              {t('trash.autoDeleteWarning', 'Items will be automatically deleted after {{days}} days', { days: 30 })}
            </Typography>
          </Box>
        )}
      </Box>

      {/* Summary Card */}
      {summary && summary.totalItems > 0 && (
        <Paper sx={{ p: 3, mb: 3, borderRadius: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <InfoIcon sx={{ mr: 1, color: 'primary.main' }} />
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              {t('trash.summary', 'Trash Summary')}
            </Typography>
          </Box>

          <Box sx={{
            display: 'grid',
            gridTemplateColumns: isMobile ? '1fr' : 'repeat(4, 1fr)',
            gap: 2,
            mb: 2
          }}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="primary.main" sx={{ fontWeight: 700 }}>
                {summary.totalItems}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {t('trash.totalItems', 'Total Items')}
              </Typography>
            </Box>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="info.main" sx={{ fontWeight: 700 }}>
                {summary.totalFiles}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {t('trash.totalFiles', 'Files')}
              </Typography>
            </Box>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="warning.main" sx={{ fontWeight: 700 }}>
                {summary.totalFolders}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {t('trash.totalFolders', 'Folders')}
              </Typography>
            </Box>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="error.main" sx={{ fontWeight: 700 }}>
                {formatFileSize(summary.totalSize)}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {t('trash.totalSize', 'Total Size')}
              </Typography>
            </Box>
          </Box>

          <Divider sx={{ my: 2 }} />

          <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Button
              variant="outlined"
              color="error"
              startIcon={<DeleteForeverIcon />}
              onClick={() => setEmptyTrashDialogOpen(true)}
              disabled={emptyingTrash}
            >
              {t('trash.emptyTrash', 'Empty Trash')}
            </Button>
          </Box>
        </Paper>
      )}

      {/* Loading */}
      {loading && (
        <Box sx={{ mb: 3 }}>
          <LinearProgress
            sx={{
              borderRadius: 1,
              height: 6,
              backgroundColor: theme.palette.mode === 'dark'
                ? 'rgba(96, 165, 250, 0.2)'
                : 'rgba(37, 99, 235, 0.1)',
              '& .MuiLinearProgress-bar': {
                backgroundColor: 'primary.main',
              },
            }}
          />
        </Box>
      )}

      {/* Trash Grid */}
      <TrashGrid
        items={items}
        onItemRestore={handleItemRestore}
        onItemPermanentDelete={handleItemPermanentDelete}
        onRefresh={loadTrashItems}
      />

      {/* Empty Trash Confirmation Dialog */}
      <Dialog
        open={emptyTrashDialogOpen}
        onClose={() => setEmptyTrashDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle sx={{ pb: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <DeleteForeverIcon color="error" />
            {t('trash.confirmEmptyTrash', 'Empty Trash')}
          </Box>
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1" sx={{ mb: 2 }}>
            {t('trash.confirmEmptyTrashMessage', 'Are you sure you want to permanently delete all items in trash? This action cannot be undone.')}
          </Typography>

          {summary && (
            <Box sx={{
              mt: 2,
              p: 2,
              backgroundColor: 'error.light',
              borderRadius: 2,
              border: '1px solid',
              borderColor: 'error.main'
            }}>
              <Typography variant="body2" sx={{ color: 'text.primary', fontWeight: 600, mb: 1 }}>
                ⚠️ {t('trash.willDelete', 'This will permanently delete:')}
              </Typography>
              <Box sx={{ ml: 2 }}>
                <Typography variant="body2" sx={{ color: 'text.primary' }}>
                  • {summary.totalFiles} {t('trash.files', 'files')}
                </Typography>
                <Typography variant="body2" sx={{ color: 'text.primary' }}>
                  • {summary.totalFolders} {t('trash.folders', 'folders')}
                </Typography>
                <Typography variant="body2" sx={{ color: 'text.primary' }}>
                  • {formatFileSize(summary.totalSize)} {t('trash.totalData', 'of data')}
                </Typography>
              </Box>
            </Box>
          )}

          <Box sx={{
            mt: 2,
            p: 2,
            backgroundColor: 'grey.50',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'grey.200'
          }}>
            <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 600 }}>
              💡 {t('trash.tip', 'Tip:')} {t('trash.tipMessage', 'You can restore individual items before emptying the trash.')}
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 2 }}>
          <Button
            onClick={() => setEmptyTrashDialogOpen(false)}
            variant="outlined"
            color="inherit"
            disabled={emptyingTrash}
          >
            {t('common.cancel')}
          </Button>
          <Button
            onClick={handleEmptyTrash}
            variant="contained"
            color="error"
            disabled={emptyingTrash}
            startIcon={emptyingTrash ? null : <DeleteForeverIcon />}
          >
            {emptyingTrash ? t('trash.emptying', 'Emptying...') : t('trash.emptyTrash', 'Empty Trash')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default TrashPage;
