import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  <PERSON>alogA<PERSON>,
  Button,
  Typography,
  Box,
  Chip,
  Divider,
  Grid,
  Card,
  CardContent,
  CircularProgress,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Paper,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Info as InfoIcon,
  LocationOn as LocationIcon,
  Computer as ComputerIcon,
  NetworkCheck as NetworkIcon,
  Schedule as ScheduleIcon,
  Security as SecurityIcon,
  Telegram as TelegramIcon,
  CameraAlt as CameraIcon,
  Image as ImageIcon,
  Videocam as VideoIcon,
} from '@mui/icons-material';
import { fileApi } from '../services/api';

interface FileMetadataDialogProps {
  open: boolean;
  onClose: () => void;
  fileId: string;
  fileName: string;
}

interface FileMetadata {
  fileInfo: {
    id: string;
    name: string;
    size: number;
    formattedSize: string;
    mimeType: string;
    uploadDate: string;
    telegramFileId: string;
  };
  fileHash: {
    md5?: string;
    sha256?: string;
  };
  uploadSession?: {
    sessionId: string;
    uploadStartTime: string;
    uploadEndTime: string;
    uploadDuration: number;
    formattedDuration: string;
    uploadSpeed: number;
    formattedSpeed: string;
    retryCount: number;
    uploadSource: string;
  };
  clientInfo?: {
    ipAddress: string;
    userAgent: string;
    referer?: string;
  };
  geolocation?: {
    latitude?: number;
    longitude?: number;
    accuracy?: number;
    timestamp?: string;
    address?: string;
    city?: string;
    country?: string;
    hasLocation: boolean;
  };
  deviceInfo?: {
    platform: string;
    browser: string;
    browserVersion: string;
    isMobile: boolean;
    isTablet: boolean;
    screenResolution?: string;
    timezone?: string;
    language?: string;
  };
  networkInfo?: {
    connectionType?: string;
    downlink?: number;
    effectiveType?: string;
    rtt?: number;
  };
  exifData?: {
    camera?: {
      make?: string;
      model?: string;
      software?: string;
      lens?: string;
      serialNumber?: string;
    };
    photoSettings?: {
      iso?: number;
      fNumber?: number;
      exposureTime?: number;
      focalLength?: number;
      focalLengthIn35mm?: number;
      flash?: any;
      whiteBalance?: any;
      meteringMode?: number;
      exposureMode?: number;
      sceneCaptureType?: number;
    };
    dateTime?: {
      dateTimeOriginal?: string;
      dateTimeDigitized?: string;
      modifyDate?: string;
      createDate?: string;
      subsecTimeOriginal?: string;
      subsecTimeDigitized?: string;
    };
    gps?: {
      latitude?: number;
      longitude?: number;
      altitude?: number;
      altitudeRef?: number;
      speed?: number;
      speedRef?: string;
      direction?: number;
      directionRef?: string;
      timestamp?: string;
      datestamp?: string;
      gpsProcessingMethod?: string;
      gpsAreaInformation?: string;
    };
    image?: {
      width?: number;
      height?: number;
      orientation?: number;
      colorSpace?: number;
      compression?: number;
      bitsPerSample?: any;
      photometricInterpretation?: number;
    };
    video?: {
      duration?: number;
      frameRate?: number;
      bitrate?: number;
      codec?: string;
      audioCodec?: string;
    };
    additional?: {
      artist?: string;
      copyright?: string;
      description?: string;
      userComment?: string;
      keywords?: any;
      subject?: string;
      title?: string;
    };
  };
  imageMetadata?: {
    format?: string;
    width?: number;
    height?: number;
    channels?: number;
    depth?: string;
    density?: number;
    hasProfile?: boolean;
    hasAlpha?: boolean;
    isProgressive?: boolean;
    compression?: string;
    resolutionUnit?: string;
    size?: number;
  };
}

const FileMetadataDialog: React.FC<FileMetadataDialogProps> = ({
  open,
  onClose,
  fileId,
  fileName,
}) => {
  const [metadata, setMetadata] = useState<FileMetadata | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (open && fileId) {
      fetchMetadata();
    }
  }, [open, fileId]);

  const fetchMetadata = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fileApi.getFileMetadata(fileId);
      setMetadata(response.data);
    } catch (err: any) {
      setError(err.message || 'Failed to load file metadata');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const MetadataRow = ({ label, value }: { label: string; value: any }) => (
    <TableRow>
      <TableCell component="th" scope="row" sx={{ fontWeight: 'bold', width: '40%' }}>
        {label}
      </TableCell>
      <TableCell>{value || 'N/A'}</TableCell>
    </TableRow>
  );

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      sx={{
        '& .MuiDialog-paper': {
          borderRadius: 3,
          maxHeight: '90vh',
        },
      }}
    >
      <DialogTitle sx={{ pb: 1 }}>
        <Box display="flex" alignItems="center" gap={1}>
          <InfoIcon color="primary" />
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            File Metadata: {fileName}
          </Typography>
        </Box>
      </DialogTitle>

      <DialogContent dividers>
        {loading && (
          <Box display="flex" justifyContent="center" p={3}>
            <CircularProgress />
          </Box>
        )}

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {metadata && (
          <Box>
            {/* Basic File Information */}
            <Accordion defaultExpanded>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Box display="flex" alignItems="center" gap={1}>
                  <InfoIcon color="primary" />
                  <Typography variant="h6">Basic Information</Typography>
                </Box>
              </AccordionSummary>
              <AccordionDetails>
                <TableContainer component={Paper} variant="outlined">
                  <Table size="small">
                    <TableBody>
                      <MetadataRow label="File Name" value={metadata.fileInfo.name} />
                      <MetadataRow label="File Size" value={metadata.fileInfo.formattedSize} />
                      <MetadataRow label="MIME Type" value={metadata.fileInfo.mimeType} />
                      <MetadataRow label="Upload Date" value={formatDate(metadata.fileInfo.uploadDate)} />
                      <MetadataRow label="Telegram File ID" value={metadata.fileInfo.telegramFileId} />
                    </TableBody>
                  </Table>
                </TableContainer>
              </AccordionDetails>
            </Accordion>

            {/* File Hashes */}
            {(metadata.fileHash.md5 || metadata.fileHash.sha256) && (
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <SecurityIcon color="primary" />
                    <Typography variant="h6">File Hashes</Typography>
                  </Box>
                </AccordionSummary>
                <AccordionDetails>
                  <TableContainer component={Paper} variant="outlined">
                    <Table size="small">
                      <TableBody>
                        {metadata.fileHash.md5 && (
                          <MetadataRow label="MD5" value={metadata.fileHash.md5} />
                        )}
                        {metadata.fileHash.sha256 && (
                          <MetadataRow label="SHA256" value={metadata.fileHash.sha256} />
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </AccordionDetails>
              </Accordion>
            )}

            {/* Upload Session */}
            {metadata.uploadSession && (
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <ScheduleIcon color="primary" />
                    <Typography variant="h6">Upload Session</Typography>
                  </Box>
                </AccordionSummary>
                <AccordionDetails>
                  <TableContainer component={Paper} variant="outlined">
                    <Table size="small">
                      <TableBody>
                        <MetadataRow label="Session ID" value={metadata.uploadSession.sessionId} />
                        <MetadataRow label="Upload Source" value={
                          <Chip
                            label={metadata.uploadSession.uploadSource}
                            size="small"
                            color="primary"
                            variant="outlined"
                          />
                        } />
                        <MetadataRow label="Start Time" value={formatDate(metadata.uploadSession.uploadStartTime)} />
                        <MetadataRow label="End Time" value={formatDate(metadata.uploadSession.uploadEndTime)} />
                        <MetadataRow label="Duration" value={metadata.uploadSession.formattedDuration} />
                        <MetadataRow label="Upload Speed" value={metadata.uploadSession.formattedSpeed} />
                        <MetadataRow label="Retry Count" value={metadata.uploadSession.retryCount} />
                      </TableBody>
                    </Table>
                  </TableContainer>
                </AccordionDetails>
              </Accordion>
            )}

            {/* Device Information */}
            {metadata.deviceInfo && (
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <ComputerIcon color="primary" />
                    <Typography variant="h6">Device Information</Typography>
                  </Box>
                </AccordionSummary>
                <AccordionDetails>
                  <TableContainer component={Paper} variant="outlined">
                    <Table size="small">
                      <TableBody>
                        <MetadataRow label="Platform" value={metadata.deviceInfo.platform} />
                        <MetadataRow label="Browser" value={`${metadata.deviceInfo.browser} ${metadata.deviceInfo.browserVersion}`} />
                        <MetadataRow label="Device Type" value={
                          <Box display="flex" gap={1}>
                            {metadata.deviceInfo.isMobile && <Chip label="Mobile" size="small" color="info" />}
                            {metadata.deviceInfo.isTablet && <Chip label="Tablet" size="small" color="info" />}
                            {!metadata.deviceInfo.isMobile && !metadata.deviceInfo.isTablet && <Chip label="Desktop" size="small" color="info" />}
                          </Box>
                        } />
                        <MetadataRow label="Screen Resolution" value={metadata.deviceInfo.screenResolution} />
                        <MetadataRow label="Timezone" value={metadata.deviceInfo.timezone} />
                        <MetadataRow label="Language" value={metadata.deviceInfo.language} />
                      </TableBody>
                    </Table>
                  </TableContainer>
                </AccordionDetails>
              </Accordion>
            )}

            {/* Geolocation */}
            {metadata.geolocation && metadata.geolocation.hasLocation && (
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <LocationIcon color="primary" />
                    <Typography variant="h6">Location Information</Typography>
                  </Box>
                </AccordionSummary>
                <AccordionDetails>
                  <TableContainer component={Paper} variant="outlined">
                    <Table size="small">
                      <TableBody>
                        <MetadataRow label="Latitude" value={metadata.geolocation.latitude?.toFixed(6)} />
                        <MetadataRow label="Longitude" value={metadata.geolocation.longitude?.toFixed(6)} />
                        <MetadataRow label="Accuracy" value={metadata.geolocation.accuracy ? `${metadata.geolocation.accuracy}m` : 'N/A'} />
                        <MetadataRow label="City" value={metadata.geolocation.city} />
                        <MetadataRow label="Country" value={metadata.geolocation.country} />
                        {metadata.geolocation.timestamp && (
                          <MetadataRow label="Location Timestamp" value={formatDate(metadata.geolocation.timestamp)} />
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </AccordionDetails>
              </Accordion>
            )}

            {/* Network Information */}
            {metadata.networkInfo && (
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <NetworkIcon color="primary" />
                    <Typography variant="h6">Network Information</Typography>
                  </Box>
                </AccordionSummary>
                <AccordionDetails>
                  <TableContainer component={Paper} variant="outlined">
                    <Table size="small">
                      <TableBody>
                        <MetadataRow label="Connection Type" value={metadata.networkInfo.connectionType} />
                        <MetadataRow label="Effective Type" value={metadata.networkInfo.effectiveType} />
                        <MetadataRow label="Downlink" value={metadata.networkInfo.downlink ? `${metadata.networkInfo.downlink} Mbps` : 'N/A'} />
                        <MetadataRow label="RTT" value={metadata.networkInfo.rtt ? `${metadata.networkInfo.rtt}ms` : 'N/A'} />
                      </TableBody>
                    </Table>
                  </TableContainer>
                </AccordionDetails>
              </Accordion>
            )}

            {/* EXIF Camera Information */}
            {metadata.exifData?.camera && (
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <CameraIcon color="primary" />
                    <Typography variant="h6">Camera Information (EXIF)</Typography>
                  </Box>
                </AccordionSummary>
                <AccordionDetails>
                  <TableContainer component={Paper} variant="outlined">
                    <Table size="small">
                      <TableBody>
                        {metadata.exifData.camera.make && (
                          <MetadataRow label="Camera Make" value={metadata.exifData.camera.make} />
                        )}
                        {metadata.exifData.camera.model && (
                          <MetadataRow label="Camera Model" value={metadata.exifData.camera.model} />
                        )}
                        {metadata.exifData.camera.lens && (
                          <MetadataRow label="Lens" value={metadata.exifData.camera.lens} />
                        )}
                        {metadata.exifData.camera.software && (
                          <MetadataRow label="Software" value={metadata.exifData.camera.software} />
                        )}
                        {metadata.exifData.camera.serialNumber && (
                          <MetadataRow label="Serial Number" value={metadata.exifData.camera.serialNumber} />
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </AccordionDetails>
              </Accordion>
            )}

            {/* EXIF Photo Settings */}
            {metadata.exifData?.photoSettings && (
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <ImageIcon color="primary" />
                    <Typography variant="h6">Photo Settings (EXIF)</Typography>
                  </Box>
                </AccordionSummary>
                <AccordionDetails>
                  <TableContainer component={Paper} variant="outlined">
                    <Table size="small">
                      <TableBody>
                        {metadata.exifData.photoSettings.iso && (
                          <MetadataRow label="ISO" value={metadata.exifData.photoSettings.iso} />
                        )}
                        {metadata.exifData.photoSettings.fNumber && (
                          <MetadataRow label="F-Number" value={`f/${metadata.exifData.photoSettings.fNumber}`} />
                        )}
                        {metadata.exifData.photoSettings.exposureTime && (
                          <MetadataRow label="Exposure Time" value={`${metadata.exifData.photoSettings.exposureTime}s`} />
                        )}
                        {metadata.exifData.photoSettings.focalLength && (
                          <MetadataRow label="Focal Length" value={`${metadata.exifData.photoSettings.focalLength}mm`} />
                        )}
                        {metadata.exifData.photoSettings.focalLengthIn35mm && (
                          <MetadataRow label="Focal Length (35mm)" value={`${metadata.exifData.photoSettings.focalLengthIn35mm}mm`} />
                        )}
                        {metadata.exifData.photoSettings.flash !== undefined && (
                          <MetadataRow label="Flash" value={metadata.exifData.photoSettings.flash} />
                        )}
                        {metadata.exifData.photoSettings.whiteBalance !== undefined && (
                          <MetadataRow label="White Balance" value={metadata.exifData.photoSettings.whiteBalance} />
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </AccordionDetails>
              </Accordion>
            )}

            {/* EXIF Date/Time Information */}
            {metadata.exifData?.dateTime && (
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <ScheduleIcon color="primary" />
                    <Typography variant="h6">Photo Date/Time (EXIF)</Typography>
                  </Box>
                </AccordionSummary>
                <AccordionDetails>
                  <TableContainer component={Paper} variant="outlined">
                    <Table size="small">
                      <TableBody>
                        {metadata.exifData.dateTime.dateTimeOriginal && (
                          <MetadataRow label="Date Taken" value={formatDate(metadata.exifData.dateTime.dateTimeOriginal)} />
                        )}
                        {metadata.exifData.dateTime.dateTimeDigitized && (
                          <MetadataRow label="Date Digitized" value={formatDate(metadata.exifData.dateTime.dateTimeDigitized)} />
                        )}
                        {metadata.exifData.dateTime.modifyDate && (
                          <MetadataRow label="Date Modified" value={formatDate(metadata.exifData.dateTime.modifyDate)} />
                        )}
                        {metadata.exifData.dateTime.createDate && (
                          <MetadataRow label="Date Created" value={formatDate(metadata.exifData.dateTime.createDate)} />
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </AccordionDetails>
              </Accordion>
            )}

            {/* EXIF GPS Information */}
            {metadata.exifData?.gps && (metadata.exifData.gps.latitude || metadata.exifData.gps.longitude) && (
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <LocationIcon color="primary" />
                    <Typography variant="h6">GPS Information (EXIF)</Typography>
                  </Box>
                </AccordionSummary>
                <AccordionDetails>
                  <TableContainer component={Paper} variant="outlined">
                    <Table size="small">
                      <TableBody>
                        {metadata.exifData.gps.latitude && (
                          <MetadataRow label="Latitude" value={metadata.exifData.gps.latitude.toFixed(6)} />
                        )}
                        {metadata.exifData.gps.longitude && (
                          <MetadataRow label="Longitude" value={metadata.exifData.gps.longitude.toFixed(6)} />
                        )}
                        {metadata.exifData.gps.altitude && (
                          <MetadataRow label="Altitude" value={`${metadata.exifData.gps.altitude}m`} />
                        )}
                        {metadata.exifData.gps.speed && (
                          <MetadataRow label="Speed" value={`${metadata.exifData.gps.speed} ${metadata.exifData.gps.speedRef || ''}`} />
                        )}
                        {metadata.exifData.gps.direction && (
                          <MetadataRow label="Direction" value={`${metadata.exifData.gps.direction}° ${metadata.exifData.gps.directionRef || ''}`} />
                        )}
                        {metadata.exifData.gps.gpsProcessingMethod && (
                          <MetadataRow label="GPS Method" value={metadata.exifData.gps.gpsProcessingMethod} />
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </AccordionDetails>
              </Accordion>
            )}

            {/* EXIF Video Information */}
            {metadata.exifData?.video && (
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <VideoIcon color="primary" />
                    <Typography variant="h6">Video Information (EXIF)</Typography>
                  </Box>
                </AccordionSummary>
                <AccordionDetails>
                  <TableContainer component={Paper} variant="outlined">
                    <Table size="small">
                      <TableBody>
                        {metadata.exifData.video.duration && (
                          <MetadataRow label="Duration" value={`${metadata.exifData.video.duration}s`} />
                        )}
                        {metadata.exifData.video.frameRate && (
                          <MetadataRow label="Frame Rate" value={`${metadata.exifData.video.frameRate} fps`} />
                        )}
                        {metadata.exifData.video.bitrate && (
                          <MetadataRow label="Bitrate" value={`${metadata.exifData.video.bitrate} bps`} />
                        )}
                        {metadata.exifData.video.codec && (
                          <MetadataRow label="Video Codec" value={metadata.exifData.video.codec} />
                        )}
                        {metadata.exifData.video.audioCodec && (
                          <MetadataRow label="Audio Codec" value={metadata.exifData.video.audioCodec} />
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </AccordionDetails>
              </Accordion>
            )}

            {/* Additional Image Metadata */}
            {metadata.imageMetadata && (
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <ImageIcon color="primary" />
                    <Typography variant="h6">Technical Image Details</Typography>
                  </Box>
                </AccordionSummary>
                <AccordionDetails>
                  <TableContainer component={Paper} variant="outlined">
                    <Table size="small">
                      <TableBody>
                        {metadata.imageMetadata.format && (
                          <MetadataRow label="Format" value={metadata.imageMetadata.format.toUpperCase()} />
                        )}
                        {metadata.imageMetadata.width && metadata.imageMetadata.height && (
                          <MetadataRow label="Dimensions" value={`${metadata.imageMetadata.width} × ${metadata.imageMetadata.height}`} />
                        )}
                        {metadata.imageMetadata.channels && (
                          <MetadataRow label="Channels" value={metadata.imageMetadata.channels} />
                        )}
                        {metadata.imageMetadata.depth && (
                          <MetadataRow label="Bit Depth" value={metadata.imageMetadata.depth} />
                        )}
                        {metadata.imageMetadata.density && (
                          <MetadataRow label="Density" value={`${metadata.imageMetadata.density} DPI`} />
                        )}
                        {metadata.imageMetadata.hasProfile !== undefined && (
                          <MetadataRow label="Color Profile" value={metadata.imageMetadata.hasProfile ? 'Yes' : 'No'} />
                        )}
                        {metadata.imageMetadata.hasAlpha !== undefined && (
                          <MetadataRow label="Alpha Channel" value={metadata.imageMetadata.hasAlpha ? 'Yes' : 'No'} />
                        )}
                        {metadata.imageMetadata.isProgressive !== undefined && (
                          <MetadataRow label="Progressive" value={metadata.imageMetadata.isProgressive ? 'Yes' : 'No'} />
                        )}
                        {metadata.imageMetadata.compression && (
                          <MetadataRow label="Compression" value={metadata.imageMetadata.compression} />
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </AccordionDetails>
              </Accordion>
            )}

            {/* Client Information */}
            {metadata.clientInfo && (
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <TelegramIcon color="primary" />
                    <Typography variant="h6">Client Information</Typography>
                  </Box>
                </AccordionSummary>
                <AccordionDetails>
                  <TableContainer component={Paper} variant="outlined">
                    <Table size="small">
                      <TableBody>
                        <MetadataRow label="IP Address" value={metadata.clientInfo.ipAddress} />
                        <MetadataRow label="User Agent" value={
                          <Typography variant="body2" sx={{ wordBreak: 'break-all', fontSize: '0.75rem' }}>
                            {metadata.clientInfo.userAgent}
                          </Typography>
                        } />
                        {metadata.clientInfo.referer && (
                          <MetadataRow label="Referer" value={metadata.clientInfo.referer} />
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </AccordionDetails>
              </Accordion>
            )}
          </Box>
        )}
      </DialogContent>

      <DialogActions sx={{ p: 2 }}>
        <Button onClick={onClose} variant="contained" color="primary">
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default FileMetadataDialog;
