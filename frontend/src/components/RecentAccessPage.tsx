import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,

  Card,
  Card<PERSON>ontent,

  IconButton,
  Chip,
  Button,
  CircularProgress,
  Alert,
  useTheme,
  useMediaQuery,
  Tooltip,
  Menu,
  MenuItem,
  FormControl,
  Select,
  InputLabel,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  Download as DownloadIcon,
  Visibility as ViewIcon,

  MoreVert as MoreVertIcon,

  Refresh as RefreshIcon,
  Close as CloseIcon,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { fileApi } from '../services/api';
import { formatFileSize, formatDate, getFileIcon, getFileCategory, isImageFile } from '../utils/helpers';
import { getPreviewUrl, handleFileDownload } from '../utils/filePreview';
import { getErrorMessage } from '../utils/errorHandler';

interface RecentFile {
  id: string;
  name: string;
  type: string;
  size: number;
  mimeType: string;
  fileType: string;
  uploadDate: string;
  parentId: string | null;
  accessedAt: string;
  accessType: 'view' | 'download' | 'preview';
  accessCount: number;
  originalFileName: string;
  telegramFileId: string;
  hasThumbnail?: boolean;
  thumbnailId?: string | null;
  dimensions?: {
    width?: number;
    height?: number;
  } | null;
}

interface RecentAccessData {
  files: RecentFile[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
}

const RecentAccessPage: React.FC = () => {
  const theme = useTheme();
  const { t } = useTranslation();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const [recentFiles, setRecentFiles] = useState<RecentFile[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    total: 0,
    limit: 20,
    offset: 0,
    hasMore: false,
  });
  const [accessTypeFilter, setAccessTypeFilter] = useState<string>('');
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedFile, setSelectedFile] = useState<RecentFile | null>(null);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewFile, setPreviewFile] = useState<RecentFile | null>(null);
  const [previewLoading, setPreviewLoading] = useState(false);

  // Function to get preview URL with current token - using utility
  const getFilePreviewUrl = (fileId: string) => {
    return getPreviewUrl(fileId);
  };

  // Handle loading state and timeout for preview
  useEffect(() => {
    if (previewFile) {
      setPreviewLoading(true);

      // Set timeout to stop loading after 10 seconds
      const timeout = setTimeout(() => {
        console.log('Preview timeout reached, stopping loading');
        setPreviewLoading(false);
      }, 10000);

      return () => clearTimeout(timeout);
    } else {
      setPreviewLoading(false);
    }
  }, [previewFile]);

  const loadRecentFiles = useCallback(async (reset = false) => {
    try {
      if (reset) {
        setLoading(true);
        setError(null);
      } else {
        setLoadingMore(true);
      }

      const offset = reset ? 0 : pagination.offset;
      const response = await fileApi.getRecentAccess(
        pagination.limit,
        offset,
        accessTypeFilter || undefined
      );

      if (response?.code === 200 && response?.data) {
        const data: RecentAccessData = response?.data;

        if (reset) {
          setRecentFiles(data.files);
        } else {
          setRecentFiles(prev => [...prev, ...data.files]);
        }

        setPagination({
          ...data.pagination,
          offset: offset + data.files.length,
        });
      } else {
        throw new Error(response?.message || t('recentAccess.error'));
      }
    } catch (err) {
      const errorMessage = getErrorMessage(err);
      setError(errorMessage);
      console.error('Error loading recent files:', err);
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  }, [accessTypeFilter, pagination.limit, pagination.offset, t]);

  useEffect(() => {
    loadRecentFiles(true);
  }, [accessTypeFilter, loadRecentFiles]);

  const handleRefresh = () => {
    loadRecentFiles(true);
  };

  const handleLoadMore = () => {
    if (pagination.hasMore && !loadingMore) {
      loadRecentFiles(false);
    }
  };

  const handleFileClick = (file: RecentFile) => {
    // Open file preview in modal - similar to FileGrid implementation
    setPreviewFile(file);
    setPreviewLoading(true);
    setPreviewOpen(true);
    setAnchorEl(null);
  };

  const handleDownload = (file: RecentFile) => {
    handleFileDownload(file);
    setAnchorEl(null);
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, file: RecentFile) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
    setSelectedFile(file);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedFile(null);
  };

  const getAccessTypeIcon = (accessType: string) => {
    switch (accessType) {
      case 'view':
        return <ViewIcon fontSize="small" />;
      case 'download':
        return <DownloadIcon fontSize="small" />;
      case 'preview':
        return <ViewIcon fontSize="small" />;
      default:
        return <ViewIcon fontSize="small" />;
    }
  };

  const getAccessTypeColor = (accessType: string) => {
    switch (accessType) {
      case 'view':
        return 'primary';
      case 'download':
        return 'success';
      case 'preview':
        return 'info';
      default:
        return 'default';
    }
  };



  // Render thumbnail with fallback logic similar to FileGrid.tsx
  const renderThumbnail = (file: RecentFile) => {
    // Priority 1: Use thumbnail if available (fastest)
    if (file.hasThumbnail && file.thumbnailId) {
      return (
        <Box
          sx={{
            width: '100%',
            height: 140,
            borderRadius: 1,
            overflow: 'hidden',
            position: 'relative',
            border: '1px solid #E5E7EB',
            backgroundColor: '#F3F4F6',
          }}
        >
          <img
            src={fileApi.thumbnailFile(file.id)}
            alt={file.name}
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
              transition: 'opacity 0.2s ease',
            }}
            onLoad={(e) => {
              // Hide loading placeholder when thumbnail loads
              const target = e.target as HTMLImageElement;
              const placeholder = target.nextElementSibling as HTMLElement;
              if (placeholder) {
                placeholder.style.display = 'none';
              }
            }}
            onError={(e) => {
              // Fallback to preview if thumbnail fails
              const target = e.target as HTMLImageElement;
              if (isImageFile(file.mimeType)) {
                target.src = fileApi.previewFile(file.id);
              } else {
                target.style.display = 'none';
                const placeholder = target.nextElementSibling as HTMLElement;
                if (placeholder) {
                  placeholder.style.display = 'flex';
                }
              }
            }}
          />
          <Box
            sx={{
              display: 'flex',
              width: '100%',
              height: '100%',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: '#F3F4F6',
              position: 'absolute',
              top: 0,
              left: 0,
              color: '#637381',
              fontSize: '48px',
            }}
          >
            {getFileIcon(file.mimeType)}
          </Box>
        </Box>
      );
    }

    // Priority 2: Use preview for images if no thumbnail
    if (isImageFile(file.mimeType)) {
      return (
        <Box
          sx={{
            width: '100%',
            height: 140,
            borderRadius: 1,
            overflow: 'hidden',
            position: 'relative',
            border: '1px solid #E5E7EB',
            backgroundColor: '#F3F4F6',
          }}
        >
          <img
            src={fileApi.previewFile(file.id)}
            alt={file.name}
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
              transition: 'opacity 0.2s ease',
            }}
            onLoad={(e) => {
              // Hide loading placeholder when image loads
              const target = e.target as HTMLImageElement;
              const placeholder = target.nextElementSibling as HTMLElement;
              if (placeholder) {
                placeholder.style.display = 'none';
              }
            }}
            onError={(e) => {
              // Show fallback icon if image fails to load
              const target = e.target as HTMLImageElement;
              target.style.display = 'none';
              const placeholder = target.nextElementSibling as HTMLElement;
              if (placeholder) {
                placeholder.style.display = 'flex';
              }
            }}
          />
          <Box
            sx={{
              display: 'flex',
              width: '100%',
              height: '100%',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: '#F3F4F6',
              position: 'absolute',
              top: 0,
              left: 0,
              color: '#637381',
              fontSize: '48px',
            }}
          >
            {getFileIcon(file.mimeType)}
          </Box>
        </Box>
      );
    }

    // Priority 3: Show appropriate icon for other file types
    const getIconColor = (mimeType: string) => {
      if (mimeType.startsWith('text/') || mimeType.includes('json')) return '#10B981';
      if (mimeType.startsWith('video/')) return '#EF4444';
      if (mimeType.startsWith('audio/')) return '#8B5CF6';
      if (mimeType.includes('pdf')) return '#DC2626';
      if (mimeType.includes('document') || mimeType.includes('word')) return '#2563EB';
      if (mimeType.includes('spreadsheet') || mimeType.includes('excel')) return '#059669';
      if (mimeType.includes('presentation') || mimeType.includes('powerpoint')) return '#EA580C';
      if (mimeType.includes('zip') || mimeType.includes('archive')) return '#7C3AED';
      return '#6B7280';
    };

    return (
      <Box
        sx={{
          width: '100%',
          height: 140,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: getIconColor(file.mimeType),
          borderRadius: 1,
          color: 'white',
          fontSize: '48px',
        }}
      >
        {getFileIcon(file.mimeType)}
      </Box>
    );
  };

  const renderPreviewContent = (file: RecentFile) => {
    const category = getFileCategory(file.mimeType);

    switch (category) {
      case "image":
        return (
          <Box
            sx={{
              position: "relative",
              display: "flex",
              justifyContent: "center",
            }}
          >
            <img
              src={getFilePreviewUrl(file.id)}
              alt={file.name}
              style={{
                maxWidth: "100%",
                maxHeight: "60vh",
                objectFit: "contain",
                borderRadius: 8,
                boxShadow: "0 4px 20px rgba(0,0,0,0.1)",
                transition: "opacity 0.3s ease",
              }}
              onLoad={() => {
                setPreviewLoading(false);
                console.log("Image loaded successfully:", file.name);
              }}
              onError={(e) => {
                setPreviewLoading(false);
                console.error("Failed to load image:", file.name);
                const target = e.target as HTMLImageElement;
                target.style.display = "none";
              }}
            />
          </Box>
        );

      case "video":
        return (
          <video
            src={getFilePreviewUrl(file.id)}
            controls
            style={{
              maxWidth: "100%",
              maxHeight: "60vh",
              borderRadius: 8,
              boxShadow: "0 4px 20px rgba(0,0,0,0.1)",
            }}
            onLoadedData={() => setPreviewLoading(false)}
            onError={() => setPreviewLoading(false)}
          >
            {t('fileGrid.videoNotSupported')}
          </video>
        );

      case "audio":
        return (
          <Box
            sx={{ textAlign: "center", py: 4, width: "100%", maxWidth: 500 }}
          >
            <Typography variant="h6" gutterBottom>
              {file.name}
            </Typography>
            <audio
              controls
              style={{ width: "100%", marginTop: 16 }}
              onLoadedData={() => setPreviewLoading(false)}
              onError={() => setPreviewLoading(false)}
            >
              <source src={getFilePreviewUrl(file.id)} type={file.mimeType} />
              {t('fileGrid.audioNotSupported')}
            </audio>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
              {t('fileGrid.size')}: {formatFileSize(file.size)}
            </Typography>
          </Box>
        );

      case "text":
      case "office":
        return (
          <Box
            sx={{
              position: "relative",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              width: "100%",
            }}
          >
            <iframe
              src={getFilePreviewUrl(file.id)}
              title={file.name}
              style={{
                width: "100%",
                height: "60vh",
                border: "none",
                borderRadius: 8,
                boxShadow: "0 4px 20px rgba(0,0,0,0.1)",
              }}
              onLoad={() => {
                setPreviewLoading(false);
                console.log("Document loaded successfully:", file.name);
              }}
              onError={() => {
                setPreviewLoading(false);
                console.error("Failed to load document:", file.name);
              }}
            />
          </Box>
        );

      case "archive":
        return (
          <Box sx={{ textAlign: "center", py: 4 }}>
            <Box sx={{ fontSize: 64, mb: 3, color: "#FF9800" }}>📦</Box>
            <Typography variant="h6" gutterBottom>
              {file.name}
            </Typography>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              {t('fileGrid.size')}: {formatFileSize(file.size)}
            </Typography>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              {t('fileGrid.type')}: {t('fileGrid.archiveFile')} ({file.mimeType})
            </Typography>
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{ mt: 2, fontStyle: "italic" }}
            >
              {t('fileGrid.downloadToExtract')}
            </Typography>
          </Box>
        );

      default:
        return (
          <Box sx={{ textAlign: "center", py: 4 }}>
            <Box sx={{ fontSize: 64, mb: 3, color: "#757575" }}>
              {getFileIcon(file.mimeType)}
            </Box>
            <Typography variant="h6" gutterBottom>
              {file.name}
            </Typography>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              {t('fileGrid.size')}: {formatFileSize(file.size)}
            </Typography>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              {t('fileGrid.type')}: {file.mimeType}
            </Typography>
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{ mt: 2, fontStyle: "italic" }}
            >
              {t('fileGrid.cannotPreview')}
            </Typography>
            <Button
              variant="contained"
              onClick={() => handleFileDownload(file)}
              sx={{ mt: 2 }}
            >
              {t('fileGrid.downloadToView')}
            </Button>
          </Box>
        );
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px' }}>
        <CircularProgress />
        <Typography variant="body1" sx={{ ml: 2 }}>
          {t('recentAccess.loading')}
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
        <Button variant="contained" onClick={handleRefresh} startIcon={<RefreshIcon />}>
          {t('recentAccess.retry')}
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ p: isMobile ? 2 : 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            {t('recentAccess.title')}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {t('recentAccess.description')}
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
          {/* Filter */}
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>{t('recentAccess.filterByType')}</InputLabel>
            <Select
              value={accessTypeFilter}
              label={t('recentAccess.filterByType')}
              onChange={(e) => setAccessTypeFilter(e.target.value)}
            >
              <MenuItem value=''>{t('recentAccess.allTypes')}</MenuItem>
              <MenuItem value="preview">{t('recentAccess.viewsOnly')}</MenuItem>
              <MenuItem value="download">{t('recentAccess.downloadsOnly')}</MenuItem>
              {/* <MenuItem value="view">{t('recentAccess.viewsOnly')}</MenuItem> */}
            </Select>
          </FormControl>

          {/* Refresh */}
          <Tooltip title={t('common.refresh')}>
            <IconButton onClick={handleRefresh} disabled={loading}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Files Grid */}
      {recentFiles.length === 0 ? (
        <Box sx={{ textAlign: 'center', py: 8 }}>
          <Typography variant="h6" color="text.secondary" gutterBottom>
            {t('recentAccess.noRecentFiles')}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {t('recentAccess.noRecentFilesDescription')}
          </Typography>
        </Box>
      ) : (
        <>
          <Box
            sx={{
              display: 'grid',
              gridTemplateColumns: {
                xs: '1fr',
                sm: 'repeat(2, 1fr)',
                md: 'repeat(3, 1fr)',
                lg: 'repeat(4, 1fr)',
              },
              gap: 2,
            }}
          >
            {recentFiles.map((file) => (
              <Box key={`${file.id}-${file.accessedAt}`}>
                <Card
                  sx={{
                    cursor: 'pointer',
                    transition: 'all 0.2s',
                    '&:hover': {
                      transform: 'translateY(-2px)',
                      boxShadow: theme.shadows[4],
                    },
                  }}
                  onClick={() => handleFileClick(file)}
                >
                  {/* Thumbnail with fallback logic */}
                  {renderThumbnail(file)}

                  <CardContent sx={{ pb: '16px !important' }}>
                    {/* File Name */}
                    <Typography
                      variant="subtitle2"
                      noWrap
                      title={file.name}
                      sx={{ fontWeight: 600, mb: 1 }}
                    >
                      {file.name}
                    </Typography>

                    {/* File Info */}
                    <Typography variant="caption" color="text.secondary" display="block">
                      {formatFileSize(file.size)}
                    </Typography>

                    {/* Access Info */}
                    <Box sx={{ mt: 1, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <Chip
                        icon={getAccessTypeIcon(file.accessType)}
                        label={t(`recentAccess.${file.accessType}ed`)}
                        size="small"
                        color={getAccessTypeColor(file.accessType) as any}
                        variant="outlined"
                      />

                      <IconButton
                        size="small"
                        onClick={(e) => handleMenuClick(e, file)}
                        sx={{ ml: 1 }}
                      >
                        <MoreVertIcon fontSize="small" />
                      </IconButton>
                    </Box>

                    {/* Last Accessed */}
                    <Typography variant="caption" color="text.secondary" display="block" sx={{ mt: 1 }}>
                      {t('recentAccess.lastAccessed')}: {formatDate(file.accessedAt)}
                    </Typography>

                    {/* Access Count */}
                    {file.accessCount > 1 && (
                      <Typography variant="caption" color="text.secondary" display="block">
                        {t('recentAccess.accessCount', { count: file.accessCount })}
                      </Typography>
                    )}
                  </CardContent>
                </Card>
              </Box>
            ))}
          </Box>

          {/* Load More */}
          {pagination.hasMore && (
            <Box sx={{ textAlign: 'center', mt: 4 }}>
              <Button
                variant="outlined"
                onClick={handleLoadMore}
                disabled={loadingMore}
                startIcon={loadingMore ? <CircularProgress size={20} /> : undefined}
              >
                {loadingMore ? t('common.loading') : t('recentAccess.loadMore')}
              </Button>
            </Box>
          )}
        </>
      )}

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => selectedFile && handleFileClick(selectedFile)}>
          <ViewIcon sx={{ mr: 1 }} fontSize="small" />
          {t('preview.title')}
        </MenuItem>
        <MenuItem onClick={() => selectedFile && handleDownload(selectedFile)}>
          <DownloadIcon sx={{ mr: 1 }} fontSize="small" />
          {t('common.download')}
        </MenuItem>
      </Menu>

      {/* Preview Modal */}
      <Dialog
        open={previewOpen}
        onClose={() => {
          setPreviewOpen(false);
          setPreviewLoading(false);
        }}
        maxWidth="md"
        fullWidth
        sx={{
          '& .MuiDialog-paper': {
            borderRadius: 3,
            minHeight: '60vh',
          }
        }}
      >
        <DialogTitle
          sx={{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            py: 2,
          }}
        >
          <Typography variant="h6" component="div" sx={{ fontWeight: 600 }}>
            {previewFile?.name}
          </Typography>
          <IconButton
            onClick={() => {
              setPreviewOpen(false);
              setPreviewLoading(false);
            }}
            sx={{ color: 'white', position: 'absolute', right: 8, top: 8 }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ p: 0, display: 'flex', flexDirection: 'column', minHeight: '50vh', position: 'relative' }}>
          {previewFile && (
            <>
              {/* Loading Overlay */}
              {previewLoading && (
                <Box
                  sx={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                    zIndex: 1,
                  }}
                >
                  <Box sx={{ textAlign: 'center' }}>
                    <CircularProgress size={60} thickness={4} />
                    <Typography variant="h6" sx={{ mt: 2, color: 'text.secondary' }}>
                      {t('preview.loading')}
                    </Typography>
                  </Box>
                </Box>
              )}

              <Box sx={{ flex: 1, display: 'flex', alignItems: 'center', justifyContent: 'center', p: 3 }}>
                {renderPreviewContent(previewFile)}
              </Box>
            </>
          )}
        </DialogContent>
        <DialogActions sx={{ p: 3, background: '#f8f9fa' }}>
          <Button
            onClick={() => {
              if (previewFile) {
                handleFileDownload(previewFile);
              }
            }}
            variant="contained"
            startIcon={<DownloadIcon />}
            sx={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              color: 'white',
              '&:hover': {
                background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
              }
            }}
          >
            {t('common.download')}
          </Button>
          <Button
            onClick={() => {
              setPreviewOpen(false);
              setPreviewLoading(false);
            }}
            variant="outlined"
          >
            {t('common.close')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default RecentAccessPage;
