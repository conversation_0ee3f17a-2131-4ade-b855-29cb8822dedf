import React, { useState, useEffect } from "react";
import {
  Box,
  Typography,
  LinearProgress,
  Popover,
  Card,
  CardContent,
  Divider,
  IconButton,
} from "@mui/material";
import {
  Storage as StorageIcon,
  Refresh as RefreshIcon,
  CloudQueue as CloudIcon,
  Folder as FolderIcon,
  InsertDriveFile as FileIcon,
} from "@mui/icons-material";
import { useTheme } from "@mui/material/styles";
import { useTranslation } from 'react-i18next';
import { fileApi } from "../services/api";

interface StorageInfo {
  storageUsed: number;
  storageQuota: number;
  availableSpace: number;
  usagePercentage: number;
  fileCount: number;
  folderCount: number;
  storageUsedFormatted: string;
  storageQuotaFormatted: string;
  availableSpaceFormatted: string;
}

interface StorageIndicatorProps {
  onStorageUpdate?: (storageInfo: StorageInfo) => void;
}

const StorageIndicator: React.FC<StorageIndicatorProps> = ({
  onStorageUpdate,
}) => {
  const theme = useTheme();
  const { t } = useTranslation();
  const [storageInfo, setStorageInfo] = useState<StorageInfo | null>(null);
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const [syncing, setSyncing] = useState(false);

  const fetchStorageInfo = React.useCallback(async () => {
    try {
      const response = await fileApi.getStorageInfo();
      if (response.code === 200 && response.data) {
        setStorageInfo(response.data);
        onStorageUpdate?.(response.data);
      }
    } catch (error) {
      console.error("Failed to fetch storage info:", error);
    }
  }, [onStorageUpdate]);

  const syncStorageStats = async () => {
    try {
      setSyncing(true);
      const response = await fileApi.syncStorageStats();
      if (response.code === 200 && response.data) {
        setStorageInfo(response.data);
        onStorageUpdate?.(response.data);
      }
    } catch (error) {
      console.error("Failed to sync storage stats:", error);
    } finally {
      setSyncing(false);
    }
  };

  useEffect(() => {
    fetchStorageInfo();
    // Refresh storage info every 30 seconds
    const interval = setInterval(fetchStorageInfo, 30000);
    return () => clearInterval(interval);
  }, [fetchStorageInfo]);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);

  if (!storageInfo) {
    return (
      <Card
        sx={{
          minWidth: 200,
          backgroundColor: theme.palette.background.paper,
          boxShadow:
            theme.palette.mode === "dark"
              ? "0 4px 20px rgba(0, 0, 0, 0.4)"
              : "0 4px 20px rgba(148, 163, 184, 0.1)",
          borderRadius: 2,
        }}
      >
        <CardContent sx={{ p: 2, "&:last-child": { pb: 2 } }}>
          <Box sx={{ display: "flex", alignItems: "center" }}>
            <StorageIcon sx={{ mr: 1, color: theme.palette.text.secondary }} />
            <Typography variant="body2" color="text.secondary">
              {t('storage.loading')}
            </Typography>
          </Box>
        </CardContent>
      </Card>
    );
  }

  const getProgressColor = (percentage: number) => {
    if (percentage < 70) return "success";
    if (percentage < 90) return "warning";
    return "error";
  };

  return (
    <>
      <Card
        onClick={handleClick}
        sx={{
          minWidth: 200,
          backgroundColor: theme.palette.background.paper,
          boxShadow:
            theme.palette.mode === "dark"
              ? "0 4px 20px rgba(0, 0, 0, 0.4)"
              : "0 4px 20px rgba(148, 163, 184, 0.3)",
          borderRadius: 2,
          cursor: "pointer",
          transition: "all 0.2s cubic-bezier(0.4, 0, 0.2, 1)",
          "&:hover": {
            transform: "translateY(-2px)",
            boxShadow:
              theme.palette.mode === "dark"
                ? "0 8px 24px rgba(0, 0, 0, 0.5)"
                : "0 8px 24px rgba(148, 163, 184, 0.3)",
          },
        }}
      >
        <CardContent sx={{ p: 2, "&:last-child": { pb: 2 } }}>
          <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
            <CloudIcon
              sx={{ mr: 1, color: theme.palette.primary.main, fontSize: 18 }}
            />
            <Typography variant="body2" fontWeight="medium">
              {t('storage.title')}
            </Typography>
            <Box sx={{ flexGrow: 1 }} />
            <Typography variant="caption" color="text.secondary">
              {storageInfo.usagePercentage}%
            </Typography>
          </Box>

          <LinearProgress
            variant="determinate"
            value={Math.min(storageInfo.usagePercentage, 100)}
            color={getProgressColor(storageInfo.usagePercentage)}
            sx={{
              height: 6,
              borderRadius: 3,
              backgroundColor:
                theme.palette.mode === "dark" ? "#374151" : "#f3f4f6",
              mb: 1,
            }}
          />

          <Box sx={{ display: "flex", justifyContent: "space-between" }}>
            <Typography variant="caption" color="text.secondary">
              {storageInfo.storageUsedFormatted}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {storageInfo.storageQuotaFormatted}
            </Typography>
          </Box>
        </CardContent>
      </Card>

      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
      >
        <Card sx={{ minWidth: 300, maxWidth: 400, '&:hover': { transform: 'translateY(0px)' } }}>
          <CardContent>
            <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
              <CloudIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
              <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
                {t('storage.usage')}
              </Typography>
              {/* <IconButton
                size="small"
                onClick={syncStorageStats}
                disabled={syncing}
                sx={{
                  color: theme.palette.text.secondary,
                  "&:hover": {
                    backgroundColor: theme.palette.action.hover,
                  },
                }}
              >
                <RefreshIcon
                  sx={{
                    animation: syncing ? "spin 1s linear infinite" : "none",
                    "@keyframes spin": {
                      "0%": { transform: "rotate(0deg)" },
                      "100%": { transform: "rotate(360deg)" },
                    },
                  }}
                />
              </IconButton> */}
            </Box>

            <Box sx={{ mb: 2 }}>
              <Box
                sx={{ display: "flex", justifyContent: "space-between", mb: 1 }}
              >
                <Typography variant="body2" color="text.secondary">
                  {t('storage.used')}: {storageInfo.storageUsedFormatted}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {storageInfo.usagePercentage}%
                </Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={Math.min(storageInfo.usagePercentage, 100)}
                color={getProgressColor(storageInfo.usagePercentage)}
                sx={{
                  height: 8,
                  borderRadius: 4,
                  backgroundColor:
                    theme.palette.mode === "dark" ? "#374151" : "#f3f4f6",
                }}
              />
              <Box
                sx={{ display: "flex", justifyContent: "space-between", mt: 1 }}
              >
                <Typography variant="caption" color="text.secondary">
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {storageInfo.storageQuotaFormatted}
                </Typography>
              </Box>
            </Box>

            <Divider sx={{ my: 2 }} />

            <Box
              sx={{ display: "flex", justifyContent: "space-between", mb: 1 }}
            >
              <Box sx={{ display: "flex", alignItems: "center" }}>
                <FileIcon
                  sx={{
                    mr: 1,
                    fontSize: 16,
                    color: theme.palette.text.secondary,
                  }}
                />
                <Typography variant="body2" color="text.secondary">
                  {t('storage.files')}
                </Typography>
              </Box>
              <Typography variant="body2" fontWeight="medium">
                {storageInfo.fileCount.toLocaleString()}
              </Typography>
            </Box>

            <Box
              sx={{ display: "flex", justifyContent: "space-between", mb: 2 }}
            >
              <Box sx={{ display: "flex", alignItems: "center" }}>
                <FolderIcon
                  sx={{
                    mr: 1,
                    fontSize: 16,
                    color: theme.palette.text.secondary,
                  }}
                />
                <Typography variant="body2" color="text.secondary">
                  {t('storage.folders')}
                </Typography>
              </Box>
              <Typography variant="body2" fontWeight="medium">
                {storageInfo.folderCount.toLocaleString()}
              </Typography>
            </Box>

            {/* <Box
              sx={{ display: "flex", justifyContent: "space-between", mb: 2 }}
            >
              <Typography variant="body2" color="text.secondary">
                Available Space
              </Typography>
              <Typography
                variant="body2"
                fontWeight="medium"
                color="success.main"
              >
                {storageInfo.availableSpaceFormatted}
              </Typography>
            </Box> */}

            {storageInfo.usagePercentage > 90 && (
              <Box
                sx={{
                  p: 1,
                  borderRadius: 1,
                  backgroundColor:
                    theme.palette.mode === "dark" ? "#7f1d1d" : "#fef2f2",
                  border: `1px solid ${
                    theme.palette.mode === "dark" ? "#dc2626" : "#fecaca"
                  }`,
                }}
              >
                <Typography variant="caption" color="error.main">
                  {t('storage.almostFull')}
                </Typography>
              </Box>
            )}
          </CardContent>
        </Card>
      </Popover>
    </>
  );
};

export default StorageIndicator;
