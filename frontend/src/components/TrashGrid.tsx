import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  IconButton,
  Menu,
  MenuItem,
  Box,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  useTheme,
  useMediaQuery,
  Paper,
  Chip,
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import {
  Folder as FolderIcon,
  MoreVert as MoreVertIcon,
  Restore as RestoreIcon,
  DeleteForever as DeleteForeverIcon,
} from '@mui/icons-material';
import { Item, FileItem } from '../types';
import {
  formatFileSize,
  formatDate,
  isImageFile,
} from '../utils/helpers';
import { fileApi } from '../services/api';
import FileTypeIcon from './FileTypeIcon';
import { alertService } from '../services/alertService';
import { getErrorMessage } from '../utils/errorHandler';

interface TrashGridProps {
  items: Item[];
  onItemRestore: (item: Item) => void;
  onItemPermanentDelete: (item: Item) => void;
  onRefresh: () => void;
}

const TrashGrid: React.FC<TrashGridProps> = ({
  items,
  onItemRestore,
  onItemPermanentDelete,
  onRefresh,
}) => {
  const theme = useTheme();
  const { t } = useTranslation();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedItem, setSelectedItem] = useState<Item | null>(null);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [actionType, setActionType] = useState<'restore' | 'permanent'>('restore');

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, item: Item) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
    setSelectedItem(item);
    console.log('Selected item:', item); // Debug log
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleRestore = () => {
    if (selectedItem) {
      console.log('Restore - Selected item:', selectedItem); // Debug log
      setActionType('restore');
      setConfirmDialogOpen(true);
    }
    handleMenuClose();
  };

  const handlePermanentDelete = () => {
    if (selectedItem) {
      console.log('Permanent delete - Selected item:', selectedItem); // Debug log
      setActionType('permanent');
      setConfirmDialogOpen(true);
    }
    handleMenuClose();
  };

  const handleConfirmAction = async () => {
    if (!selectedItem) return;

    try {
      if (actionType === 'restore') {
        await fileApi.restoreFromTrash(selectedItem.id);
        alertService.success(t('trash.itemRestored', 'Item restored successfully'));
        onItemRestore(selectedItem);
      } else {
        await fileApi.permanentlyDeleteItem(selectedItem.id);
        alertService.success(t('trash.itemPermanentlyDeleted', 'Item permanently deleted'));
        onItemPermanentDelete(selectedItem);
      }
      onRefresh();
    } catch (error: any) {
      alertService.error(getErrorMessage(error, `Failed to ${actionType === 'restore' ? 'restore' : 'permanently delete'} item`));
    } finally {
      // Always close dialog and clear selection
      setConfirmDialogOpen(false);
      setSelectedItem(null);
    }
  };

  const getItemIcon = (item: Item) => {
    if (item.type === 'folder') {
      return <FolderIcon sx={{ fontSize: 48, color: '#0061FF' }} />;
    }

    const fileItem = item as FileItem;
    if (isImageFile(fileItem.mimeType)) {
      return (
        <Box
          sx={{
            width: 48,
            height: 48,
            borderRadius: 1,
            overflow: 'hidden',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'grey.100',
          }}
        >
          <img
            src={fileApi.thumbnailFile(fileItem.id)}
            alt={fileItem.name}
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
            }}
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.style.display = 'none';
              target.parentElement!.innerHTML = '<div style="color: #666;"><svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/></svg></div>';
            }}
          />
        </Box>
      );
    }

    return <FileTypeIcon mimeType={fileItem.mimeType} fontSize="large" sx={{ fontSize: 48 }} />;
  };

  const getDaysInTrash = (deletedAt: string) => {
    const deleted = new Date(deletedAt);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - deleted.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  if (items.length === 0) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: 300,
          textAlign: 'center',
          color: 'text.secondary',
        }}
      >
        <DeleteForeverIcon sx={{ fontSize: 64, mb: 2, opacity: 0.5 }} />
        <Typography variant="h6" gutterBottom>
          {t('trash.empty', 'Trash is empty')}
        </Typography>
        <Typography variant="body2">
          {t('trash.emptyDescription', 'Deleted items will appear here')}
        </Typography>
      </Box>
    );
  }

  console.log('Items:', items, selectedItem);
  return (
    <>
      <Box
        sx={{
          display: 'grid',
          gridTemplateColumns: isMobile
            ? 'repeat(auto-fill, minmax(150px, 1fr))'
            : 'repeat(auto-fill, minmax(200px, 1fr))',
          gap: 2,
          mt: 2,
        }}
      >
        {items.map((item) => (
          <Paper
            key={item.id}
            sx={{
              p: 2,
              borderRadius: 2,
              cursor: 'pointer',
              transition: 'all 0.2s ease-in-out',
              border: '1px solid',
              borderColor: 'divider',
              backgroundColor: 'background.paper',
              '&:hover': {
                borderColor: 'primary.main',
                boxShadow: theme.shadows[4],
                transform: 'translateY(-2px)',
              },
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 1 }}>
              <Box sx={{ flex: 1, display: 'flex', alignItems: 'center' }}>
                {getItemIcon(item)}
                <IconButton
                  size="small"
                  onClick={(e) => handleMenuClick(e, item)}
                  sx={{ ml: 'auto' }}
                >
                  <MoreVertIcon />
                </IconButton>
              </Box>
            </Box>

            <Typography
              variant="body2"
              sx={{
                fontWeight: 500,
                mb: 1,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }}
            >
              {item.name}
            </Typography>

            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
              {item.type === 'file' && (
                <Typography variant="caption" color="text.secondary">
                  {formatFileSize((item as FileItem).size)}
                </Typography>
              )}
              <Typography variant="caption" color="text.secondary">
                {t('trash.deletedOn', 'Deleted')}: {formatDate((item as any).deletedAt)}
              </Typography>
              <Chip
                label={t('trash.daysInTrash', '{{days}} days in trash', {
                  days: getDaysInTrash((item as any).deletedAt)
                })}
                size="small"
                variant="outlined"
                color={getDaysInTrash((item as any).deletedAt) > 25 ? 'error' : 'default'}
                sx={{
                  alignSelf: 'flex-start',
                  fontSize: '0.7rem',
                  '& .MuiChip-label': {
                    px: 1
                  }
                }}
              />
            </Box>
          </Paper>
        ))}
      </Box>

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={() => {
          setSelectedItem(null);
        }}
      >
        <MenuItem onClick={handleRestore}>
          <RestoreIcon sx={{ mr: 1 }} />
          {t('trash.restore', 'Restore')}
        </MenuItem>
        <MenuItem onClick={handlePermanentDelete} sx={{ color: 'error.main' }}>
          <DeleteForeverIcon sx={{ mr: 1 }} />
          {t('trash.permanentDelete', 'Delete Forever')}
        </MenuItem>
      </Menu>

      <Dialog
        open={confirmDialogOpen}
        onClose={() => setConfirmDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle sx={{ pb: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {actionType === 'restore' ? (
              <RestoreIcon color="primary" />
            ) : (
              <DeleteForeverIcon color="error" />
            )}
            {actionType === 'restore'
              ? t('trash.confirmRestore', 'Restore Item')
              : t('trash.confirmPermanentDelete', 'Permanently Delete Item')
            }
          </Box>
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1" sx={{ mb: 2 }}>
            {actionType === 'restore'
              ? t('trash.confirmRestoreMessage', 'Are you sure you want to restore "{{name}}"?', { name: selectedItem?.name || 'Unknown' })
              : t('trash.confirmPermanentDeleteMessage', 'Are you sure you want to permanently delete "{{name}}"? This action cannot be undone.', { name: selectedItem?.name || 'Unknown' })
            }
          </Typography>

          {selectedItem && (
            <Box sx={{
              p: 2,
              backgroundColor: 'grey.50',
              borderRadius: 1,
              border: '1px solid',
              borderColor: 'grey.200'
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                {getItemIcon(selectedItem)}
                <Box>
                  <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                    {selectedItem.name}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {selectedItem.type === 'file'
                      ? `${t('common.file')} • ${formatFileSize((selectedItem as FileItem).size)}`
                      : t('common.folder')
                    }
                  </Typography>
                  <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                    {t('trash.deletedOn', 'Deleted')}: {formatDate((selectedItem as any).deletedAt)}
                  </Typography>
                </Box>
              </Box>
            </Box>
          )}

          {actionType === 'permanent' && (
            <Box sx={{
              mt: 2,
              p: 2,
              backgroundColor: 'error.light',
              borderRadius: 1,
              border: '1px solid',
              borderColor: 'error.main'
            }}>
              <Typography variant="body2" sx={{ color: 'text.primary', fontWeight: 600 }}>
                ⚠️ {t('trash.warningPermanentDelete', 'Warning: This action cannot be undone!')}
              </Typography>
              <Typography variant="body2" sx={{ color: 'text.primary' }}>
                {t('trash.warningPermanentDeleteDescription', 'The item will be permanently removed from your storage.')}
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 2 }}>
          <Button
            onClick={() => setConfirmDialogOpen(false)}
            variant="outlined"
            color="inherit"
          >
            {t('common.cancel')}
          </Button>
          <Button
            onClick={handleConfirmAction}
            variant="contained"
            color={actionType === 'restore' ? 'primary' : 'error'}
            startIcon={actionType === 'restore' ? <RestoreIcon /> : <DeleteForeverIcon />}
          >
            {actionType === 'restore'
              ? t('trash.restore', 'Restore')
              : t('trash.permanentDelete', 'Delete Forever')
            }
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default TrashGrid;
