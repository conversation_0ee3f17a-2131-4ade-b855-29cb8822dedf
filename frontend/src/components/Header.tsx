import React, { useState, useRef } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  TextField,
  InputAdornment,
  IconButton,
  Box,
  Avatar,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Divider,
  useMediaQuery,
} from '@mui/material';
import {
  Search as SearchIcon,
  Clear as ClearIcon,
  NotificationsNone as NotificationIcon,
  HelpOutline as HelpIcon,
  Settings as SettingsIcon,
  Person as PersonIcon,
  ExitToApp as LogoutIcon,
  Menu as MenuIcon,
} from '@mui/icons-material';
import { useTheme } from '@mui/material/styles';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useApp } from '../contexts/AppContext';
import { fileApi } from '../services/api';

interface HeaderProps {
  onUploadClick: () => void;
  onToggleTheme: () => void;
  onMobileMenuClick?: () => void;
  user?: any;
  onLogout?: () => void;
}


const Header: React.FC<HeaderProps> = ({ onUploadClick, onToggleTheme, onMobileMenuClick, user, onLogout }) => {
  const { state, dispatch } = useApp();
  const theme = useTheme();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [searchInput, setSearchInput] = useState(state.searchQuery);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const open = Boolean(anchorEl);

  const handleSearch = async (query: string) => {
    if (query.trim().length < 2) {
      dispatch({ type: 'CLEAR_SEARCH' });
      return;
    }

    dispatch({ type: 'SET_SEARCHING', payload: true });
    dispatch({ type: 'SET_SEARCH_QUERY', payload: query });

    try {
      console.log('Searching for:', query);
      const response = await fileApi.search(query);
      console.log('Search response:', response);
      if (response.code === 200 && response.data) {
        const allItems = [...response.data.folders, ...response.data.files];
        console.log('Search results:', allItems);
        dispatch({ type: 'SET_SEARCH_RESULTS', payload: allItems });
      }
    } catch (error) {
      console.error('Search error:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Search failed' });
    } finally {
      dispatch({ type: 'SET_SEARCHING', payload: false });
    }
  };

  const handleSearchInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setSearchInput(value);

    // Clear previous timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // Debounce search
    searchTimeoutRef.current = setTimeout(() => {
      handleSearch(value);
    }, 500);
  };

  const handleClearSearch = () => {
    setSearchInput('');
    dispatch({ type: 'CLEAR_SEARCH' });
  };

  const testSearch = () => {
    console.log('Testing search with "heyu"');
    setSearchInput('heyu');
    handleSearch('heyu');
  };

  const handleProfileClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleProfileClose = () => {
    setAnchorEl(null);
  };

  return (
    <AppBar
      position="fixed"
      elevation={0}
      sx={{
        backgroundColor: theme.palette.background.paper,
        borderBottom: `1px solid ${theme.palette.divider}`,
        height: 64,
      }}
    >
      <Toolbar sx={{ py: 1, minHeight: '64px !important', display: 'flex' }}>
        {/* Mobile Menu Button */}
        {isMobile && (
          <IconButton
            edge="start"
            color="inherit"
            aria-label="menu"
            onClick={onMobileMenuClick}
            sx={{ mr: 2 }}
          >
            <MenuIcon />
          </IconButton>
        )}

        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            cursor: 'pointer',
            '&:hover': {
              opacity: 0.8,
            },
            transition: 'opacity 0.2s ease',
          }}
          onClick={() => {
            // Navigate to root folder completely
            dispatch({ type: 'SET_CURRENT_FOLDER', payload: null });
            dispatch({ type: 'SET_BREADCRUMBS', payload: [{ id: null, name: t('sidebar.home') }] });
            dispatch({ type: 'CLEAR_SEARCH' });
            navigate('/');
          }}
        >
          <Box
            sx={{
              width: 32,
              height: 32,
              borderRadius: 1,
              background: 'linear-gradient(135deg, #2563EB 0%, #3B82F6 50%, #60A5FA 100%)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mr: 2,
              position: 'relative',
              overflow: 'hidden',
            }}
          >
            {/* Camera lens */}
            <Box
              sx={{
                width: 18,
                height: 18,
                borderRadius: '50%',
                backgroundColor: 'white',
                border: '1px solid #1E40AF',
                position: 'relative',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              {/* Inner lens ring */}
              <Box
                sx={{
                  width: 12,
                  height: 12,
                  borderRadius: '50%',
                  border: '1px solid #2563EB',
                  position: 'relative',
                }}
              >
                {/* Center dot */}
                <Box
                  sx={{
                    width: 4,
                    height: 4,
                    borderRadius: '50%',
                    backgroundColor: '#1E40AF',
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                  }}
                />
              </Box>
            </Box>
          </Box>
          {!isMobile && (
            <Typography
              variant="h6"
              component="div"
              sx={{
                fontWeight: 600,
                color: theme.palette.text.primary,
                fontSize: '1.25rem',
              }}
            >
              {t('header.brand')}
            </Typography>
          )}
        </Box>

        <Box sx={{
          flexGrow: 1,
          maxWidth: isMobile ? 'none' : 600,
          mx: isMobile ? 2 : 12
        }}>
          <TextField
            fullWidth
            size="small"
            placeholder={isMobile ? t('header.search') : t('header.searchPlaceholder')}
            value={searchInput}
            onChange={handleSearchInputChange}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon sx={{ color: '#637381' }} />
                </InputAdornment>
              ),
              endAdornment: searchInput && (
                <InputAdornment position="end">
                  <IconButton size="small" onClick={handleClearSearch}>
                    <ClearIcon sx={{ color: '#637381' }} />
                  </IconButton>
                  <IconButton onClick={testSearch} sx={{ ml: 1 }}>
                    🔍
                  </IconButton>
                </InputAdornment>
              ),
              sx: {
                backgroundColor: '#F9FAFB',
                borderRadius: 2,
                '&:hover': {
                  backgroundColor: '#F3F4F6',
                },
                '& .MuiOutlinedInput-notchedOutline': {
                  border: '1px solid #E5E7EB',
                },
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  border: '1px solid #D1D5DB',
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  border: '2px solid #0061FF',
                },
                '& input': {
                  color: '#1E1E1E',
                  fontSize: '0.875rem',
                  '&::placeholder': {
                    color: '#9CA3AF',
                  },
                },
              },
            }}
          />
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', ml: 'auto', gap: isMobile ? 0.5 : 1 }}>
          {/* <IconButton
            sx={{
              color: theme.palette.text.secondary,
              '&:hover': {
                backgroundColor: theme.palette.action.hover,
              },
            }}
          >
            <GridIcon />
          </IconButton> */}

          {!isMobile && (
            <IconButton
              sx={{
                color: theme.palette.text.secondary,
                '&:hover': {
                  backgroundColor: theme.palette.action.hover,
                },
              }}
            >
              <NotificationIcon />
            </IconButton>
          )}

          {/* <IconButton
            onClick={onToggleTheme}
            sx={{
              color: theme.palette.text.secondary,
              '&:hover': {
                backgroundColor: theme.palette.action.hover,
              },
            }}
          >
            {theme.palette.mode === 'dark' ? (
              <Brightness7Icon />
            ) : (
              <Brightness4Icon />
            )}
          </IconButton> */}

          {!isMobile && (
            <IconButton
              onClick={() => navigate('/settings')}
              sx={{
                color: theme.palette.text.secondary,
                '&:hover': {
                  backgroundColor: theme.palette.action.hover,
                },
              }}
            >
              <SettingsIcon />
            </IconButton>
          )}

          <IconButton
            sx={{
              color: theme.palette.text.secondary,
              '&:hover': {
                backgroundColor: theme.palette.action.hover,
              },
            }}
          >
            <HelpIcon />
          </IconButton>

          <IconButton
            onClick={handleProfileClick}
            sx={{
              ml: 1,
              '&:hover': {
                backgroundColor: theme.palette.action.hover,
              },
            }}
          >
            <Avatar
              src={user?.avatar || user?.profilePicture}
              sx={{
                width: 32,
                height: 32,
                backgroundColor: '#2563EB',
                fontSize: '0.875rem',
                fontWeight: 600,
              }}
            >
              {user?.name?.charAt(0)?.toUpperCase() || user?.username?.charAt(0)?.toUpperCase() || 'U'}
            </Avatar>
          </IconButton>

          <Menu
            anchorEl={anchorEl}
            open={open}
            onClose={handleProfileClose}
            PaperProps={{
              sx: {
                boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
                border: '1px solid #E5E7EB',
                borderRadius: 2,
                mt: 1,
                minWidth: 200,
              },
            }}
          >
            <MenuItem onClick={handleProfileClose}>
              <ListItemIcon>
                <PersonIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>{t('header.profile')}</ListItemText>
            </MenuItem>
            <MenuItem onClick={() => {
              handleProfileClose();
              navigate('/settings');
            }}>
              <ListItemIcon>
                <SettingsIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>{t('header.settings')}</ListItemText>
            </MenuItem>
            <Divider />
            <MenuItem onClick={() => {
              handleProfileClose();
              onLogout?.();
            }}>
              <ListItemIcon>
                <LogoutIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>{t('header.signOut')}</ListItemText>
            </MenuItem>
          </Menu>
        </Box>
      </Toolbar>
    </AppBar>
  );
};

export default Header;
