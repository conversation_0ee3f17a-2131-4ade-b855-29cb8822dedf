import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Button,
} from '@mui/material';
import {
  Language as LanguageIcon,
  Palette as PaletteIcon,
  Person as PersonIcon,
  Security as SecurityIcon,
  Notifications as NotificationsIcon,
  Storage as StorageIcon,
  Info as InfoIcon,
  VpnKey as VpnKeyIcon,
} from '@mui/icons-material';
import { useTheme } from '@mui/material/styles';
import { useTranslation } from 'react-i18next';
import { alertService } from '../services/alertService';
import ChangePasswordDialog from './ChangePasswordDialog';

interface SettingsPageProps {
  onThemeChange: () => void;
  currentTheme: 'light' | 'dark';
}

const SettingsPage: React.FC<SettingsPageProps> = ({ onThemeChange, currentTheme }) => {
  const theme = useTheme();
  const { t, i18n } = useTranslation();
  const [changePasswordOpen, setChangePasswordOpen] = useState(false);

  // Get current user from localStorage
  const getCurrentUser = () => {
    try {
      const userData = localStorage.getItem('user');
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('Failed to parse user data:', error);
      return null;
    }
  };

  const currentUser = getCurrentUser();
  const isLocalUser = currentUser && currentUser.provider === 'local';

  const handleLanguageChange = (language: string) => {
    i18n.changeLanguage(language);
    alertService.success(t('settings.languageChanged'));
  };

  const handleThemeToggle = () => {
    onThemeChange();
    alertService.success(t('settings.themeChanged'));
  };

  const handleChangePasswordClick = () => {
    setChangePasswordOpen(true);
  };

  const languages = [
    { code: 'en', name: t('settings.english'), flag: '🇺🇸' },
    { code: 'vi', name: t('settings.vietnamese'), flag: '🇻🇳' },
    { code: 'zh', name: t('settings.chinese'), flag: '🇨🇳' },
  ];

  return (
    <Box sx={{
      minHeight: '100vh',
      bgcolor: 'background.default',
      color: 'text.primary',
      pt: '64px', // Header height
    }}>
      <Box sx={{
        maxWidth: 800,
        mx: 'auto',
        p: 3,
      }}>
        <Typography variant="h4" sx={{ mb: 3, fontWeight: 600 }}>
          {t('settings.title')}
        </Typography>

        {/* Language Settings */}
        <Paper sx={{ mb: 3, p: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <LanguageIcon sx={{ mr: 2, color: 'primary.main' }} />
            <Typography variant="h6">{t('settings.language')}</Typography>
          </Box>
          <FormControl fullWidth>
            <InputLabel>{t('settings.language')}</InputLabel>
            <Select
              value={i18n.language || 'en'}
              label={t('settings.language')}
              onChange={(e) => handleLanguageChange(e.target.value)}
            >
              {languages.map((lang) => (
                <MenuItem key={lang.code} value={lang.code}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <span>{lang.flag}</span>
                    <span>{lang.name}</span>
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Paper>

        {/* Theme Settings */}
        <Paper sx={{ mb: 3, p: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <PaletteIcon sx={{ mr: 2, color: 'primary.main' }} />
            <Typography variant="h6">{t('settings.theme')}</Typography>
          </Box>
          <FormControlLabel
            control={
              <Switch
                checked={currentTheme === 'dark'}
                onChange={handleThemeToggle}
                color="primary"
              />
            }
            label={currentTheme === 'dark' ? t('settings.dark') : t('settings.light')}
          />
        </Paper>

        {/* Security Settings */}
        {isLocalUser && (
          <Paper sx={{ mb: 3, p: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <SecurityIcon sx={{ mr: 2, color: 'primary.main' }} />
              <Typography variant="h6">{t('settings.security')}</Typography>
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', p: 2, bgcolor: theme.palette.action.hover, borderRadius: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <VpnKeyIcon sx={{ mr: 2, color: 'primary.main' }} />
                <Box>
                  <Typography variant="subtitle1">{t('auth.changePassword')}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    {t('auth.passwordChangeDescription')}
                  </Typography>
                </Box>
              </Box>
              <Button
                variant="outlined"
                onClick={handleChangePasswordClick}
                sx={{ minWidth: 120 }}
              >
                {t('auth.changePassword')}
              </Button>
            </Box>
          </Paper>
        )}

        {/* Other Settings Sections */}
        <Paper sx={{ mb: 3 }}>
          <List>
            <ListItem>
              <ListItemIcon>
                <PersonIcon color="primary" />
              </ListItemIcon>
              <ListItemText
                primary={t('settings.profile')}
                secondary={t('settings.profileSecondary')}
              />
            </ListItem>
            <Divider />
            <ListItem>
              <ListItemIcon>
                <NotificationsIcon color="primary" />
              </ListItemIcon>
              <ListItemText
                primary={t('settings.notifications')}
                secondary={t('settings.notificationsSecondary')}
              />
            </ListItem>
            <Divider />
            <ListItem>
              <ListItemIcon>
                <StorageIcon color="primary" />
              </ListItemIcon>
              <ListItemText
                primary={t('settings.storage')}
                secondary={t('settings.storageSecondary')}
              />
            </ListItem>
            <Divider />
            <ListItem>
              <ListItemIcon>
                <InfoIcon color="primary" />
              </ListItemIcon>
              <ListItemText
                primary={t('settings.about')}
                secondary={t('settings.aboutSecondary')}
              />
            </ListItem>
          </List>
        </Paper>

        {/* Change Password Dialog - Only for local users */}
        {isLocalUser && (
          <ChangePasswordDialog
            open={changePasswordOpen}
            onClose={() => setChangePasswordOpen(false)}
          />
        )}

      </Box>
    </Box>
  );
};

export default SettingsPage;
