import React from 'react';
import {
  Image as ImageIcon,
  VideoFile as VideoFileIcon,
  AudioFile as AudioFileIcon,
  PictureAsPdf as PictureAsPdfIcon,
  Description as DescriptionIcon,
  TableChart as TableChartIcon,
  Slideshow as SlideshowIcon,
  Code as CodeIcon,
  DataObject as DataObjectIcon,
  TextSnippet as TextSnippetIcon,
  Archive as ArchiveIcon,
  Settings as SettingsIcon,
  InsertDriveFile as InsertDriveFileIcon,
  Language as HtmlIcon,
  Palette as CssIcon,
  Animation as GifIcon,
} from '@mui/icons-material';
import { getFileIconName } from '../utils/helpers';

interface FileTypeIconProps {
  mimeType: string;
  fontSize?: 'small' | 'medium' | 'large' | 'inherit';
  sx?: any;
}

const FileTypeIcon: React.FC<FileTypeIconProps> = ({ mimeType, fontSize = 'medium', sx }) => {
  const iconName = getFileIconName(mimeType);

  const iconProps = {
    fontSize,
    sx,
  };

  switch (iconName) {
    case 'Image':
      return <ImageIcon {...iconProps} />;
    case 'Gif':
      return <GifIcon {...iconProps} />;
    case 'VideoFile':
      return <VideoFileIcon {...iconProps} />;
    case 'AudioFile':
      return <AudioFileIcon {...iconProps} />;
    case 'PictureAsPdf':
      return <PictureAsPdfIcon {...iconProps} />;
    case 'Description':
      return <DescriptionIcon {...iconProps} />;
    case 'TableChart':
      return <TableChartIcon {...iconProps} />;
    case 'Slideshow':
      return <SlideshowIcon {...iconProps} />;
    case 'Code':
      return <CodeIcon {...iconProps} />;
    case 'DataObject':
      return <DataObjectIcon {...iconProps} />;
    case 'Css':
      return <CssIcon {...iconProps} />;
    case 'Html':
      return <HtmlIcon {...iconProps} />;
    case 'TextSnippet':
      return <TextSnippetIcon {...iconProps} />;
    case 'Archive':
      return <ArchiveIcon {...iconProps} />;
    case 'Settings':
      return <SettingsIcon {...iconProps} />;
    default:
      return <InsertDriveFileIcon {...iconProps} />;
  }
};

export default FileTypeIcon;
