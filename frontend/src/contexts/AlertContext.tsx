import React, { createContext, useContext, useReducer, ReactNode } from 'react';

export interface AlertMessage {
  id: string;
  message: string;
  severity: 'success' | 'error' | 'warning' | 'info';
  autoHideDuration?: number;
}

interface AlertState {
  alerts: AlertMessage[];
}

type AlertAction =
  | { type: 'ADD_ALERT'; payload: AlertMessage }
  | { type: 'REMOVE_ALERT'; payload: string };

const initialState: AlertState = {
  alerts: [],
};

const alertReducer = (state: AlertState, action: AlertAction): AlertState => {
  switch (action.type) {
    case 'ADD_ALERT':
      return {
        ...state,
        alerts: [...state.alerts, action.payload],
      };
    case 'REMOVE_ALERT':
      return {
        ...state,
        alerts: state.alerts.filter(alert => alert.id !== action.payload),
      };
    default:
      return state;
  }
};

interface AlertContextType {
  state: AlertState;
  dispatch: React.Dispatch<AlertAction>;
}

const AlertContext = createContext<AlertContextType | undefined>(undefined);

export const AlertProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(alertReducer, initialState);

  return (
    <AlertContext.Provider value={{ state, dispatch }}>
      {children}
    </AlertContext.Provider>
  );
};

export const useAlert = () => {
  const context = useContext(AlertContext);
  if (context === undefined) {
    throw new Error('useAlert must be used within an AlertProvider');
  }
  return context;
};

export default AlertContext;
