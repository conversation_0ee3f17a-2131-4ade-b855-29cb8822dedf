import { AlertMessage } from '../contexts/AlertContext';

class AlertService {
  private dispatch: React.Dispatch<any> | null = null;

  // Khởi tạo service với dispatch function từ AlertContext
  init(dispatch: React.Dispatch<any>) {
    this.dispatch = dispatch;
  }

  // Tạo ID unique cho mỗi alert
  private generateId(): string {
    return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Hiển thị alert success
  success(message: string, autoHideDuration: number = 6000) {
    this.showAlert(message, 'success', autoHideDuration);
  }

  // Hiển thị alert error
  error(message: string, autoHideDuration: number = 8000) {
    this.showAlert(message, 'error', autoHideDuration);
  }

  // Hiển thị alert warning
  warning(message: string, autoHideDuration: number = 6000) {
    this.showAlert(message, 'warning', autoHideDuration);
  }

  // Hiển thị alert info
  info(message: string, autoHideDuration: number = 6000) {
    this.showAlert(message, 'info', autoHideDuration);
  }

  // Hàm chính để hiển thị alert
  private showAlert(
    message: string, 
    severity: 'success' | 'error' | 'warning' | 'info',
    autoHideDuration: number = 6000
  ) {
    if (!this.dispatch) {
      console.warn('AlertService not initialized. Call alertService.init(dispatch) first.');
      return;
    }

    const alert: AlertMessage = {
      id: this.generateId(),
      message,
      severity,
      autoHideDuration,
    };

    // Thêm alert vào state
    this.dispatch({
      type: 'ADD_ALERT',
      payload: alert,
    });

    // Tự động xóa alert sau thời gian quy định
    if (autoHideDuration > 0) {
      setTimeout(() => {
        this.removeAlert(alert.id);
      }, autoHideDuration);
    }
  }

  // Xóa alert theo ID
  removeAlert(id: string) {
    if (!this.dispatch) {
      return;
    }

    this.dispatch({
      type: 'REMOVE_ALERT',
      payload: id,
    });
  }

  // Xóa tất cả alerts
  clearAll() {
    if (!this.dispatch) {
      return;
    }

    // Lấy tất cả alert IDs hiện tại và xóa từng cái
    // Vì chúng ta không có access trực tiếp đến state ở đây
    // Chúng ta sẽ implement này trong AlertContainer
  }
}

// Export singleton instance
export const alertService = new AlertService();
export default alertService;
