# Global Alert System

Hệ thống alert global cho phép bạn hiển thị thông báo từ bất kỳ đâu trong ứng dụng mà không cần import và gọi trong render.

## Cách sử dụng

### 1. Import alertService

```typescript
import { alertService } from '../services/alertService';
// hoặc
import { alertService } from '../utils/alertDemo'; // có sẵn export
```

### 2. Sử dụng các phương thức

```typescript
// Hiển thị thông báo thành công
alertService.success('Tải file thành công!');

// Hiển thị thông báo lỗi
alertService.error('Có lỗi xảy ra khi tải file!');

// Hiển thị thông báo cảnh báo
alertService.warning('File này có thể không an toàn!');

// Hiển thị thông báo thông tin
alertService.info('<PERSON><PERSON> xử lý file...');
```

### 3. Tùy chỉnh thời gian hiển thị

```typescript
// Mặc định: success = 6s, error = 8s, warning = 6s, info = 6s
alertService.success('Thành công!', 3000); // Hiển thị 3 giây
alertService.error('Lỗi!', 10000); // Hiển thị 10 giây
alertService.warning('Cảnh báo!', 5000); // Hiển thị 5 giây
alertService.info('Thông tin!', 4000); // Hiển thị 4 giây
```

### 4. Sử dụng trong các tình huống khác nhau

#### Trong async/await functions:
```typescript
const uploadFile = async () => {
  try {
    await fileApi.upload(file);
    alertService.success('Tải file thành công!');
  } catch (error) {
    alertService.error(`Lỗi: ${error.message}`);
  }
};
```

#### Trong event handlers:
```typescript
const handleClick = () => {
  alertService.info('Đang xử lý...');
  // Xử lý logic
  setTimeout(() => {
    alertService.success('Hoàn thành!');
  }, 2000);
};
```

#### Trong utility functions:
```typescript
// utils/fileHelper.ts
import { alertService } from '../services/alertService';

export const validateFile = (file: File) => {
  if (file.size > 100 * 1024 * 1024) {
    alertService.warning('File quá lớn! Tối đa 100MB');
    return false;
  }
  return true;
};
```

## Ưu điểm

1. **Không cần import trong component**: Chỉ cần import service và gọi trực tiếp
2. **Không cần quản lý state**: Không cần useState cho snackbar
3. **Tự động stack**: Nhiều alert sẽ xếp chồng lên nhau
4. **Tự động ẩn**: Alert tự động biến mất sau thời gian quy định
5. **Responsive**: Hoạt động tốt trên mobile và desktop
6. **Consistent styling**: Thiết kế thống nhất với theme của app

## Demo

Trong Header có button 🔔 để test tất cả loại alert.

## Cấu trúc hệ thống

- `AlertContext.tsx`: Context quản lý state của alerts
- `alertService.ts`: Service để gọi alerts từ bất kỳ đâu
- `AlertContainer.tsx`: Component hiển thị alerts
- `App.tsx`: Wrap app với AlertProvider và AlertContainer

## Migration từ hệ thống cũ

### Trước:
```typescript
const [snackbar, setSnackbar] = useState({
  open: false, 
  message: '', 
  severity: 'success'
});

// Trong JSX
<Snackbar open={snackbar.open} ...>
  <Alert severity={snackbar.severity}>
    {snackbar.message}
  </Alert>
</Snackbar>

// Sử dụng
setSnackbar({
  open: true,
  message: 'Thành công!',
  severity: 'success'
});
```

### Sau:
```typescript
// Chỉ cần import và gọi
import { alertService } from '../services/alertService';

alertService.success('Thành công!');
```

## Lưu ý

- AlertService phải được khởi tạo trong AlertContainer trước khi sử dụng
- Nếu gọi alertService trước khi app render AlertContainer, sẽ có warning trong console
- Alerts sẽ hiển thị ở góc dưới bên trái màn hình
- Có thể click X để đóng alert sớm
