<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Telegram File Storage - File Manager</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* Dark theme variables */
        :root[data-theme="dark"] {
            --primary-color: #3b82f6;
            --secondary-color: #60a5fa;
            --accent-color: #93c5fd;
            --background-color: #111827;
            --text-primary: #f3f4f6;
            --text-secondary: #d1d5db;
            --border-color: #374151;
        }

        /* Theme toggle button */
        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 10px;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .theme-toggle:hover {
            transform: scale(1.1);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .container {
                border-radius: 10px;
            }

            .header {
                padding: 20px;
            }

            .header h1 {
                font-size: 1.8em;
            }

            .toolbar {
                flex-direction: column;
                align-items: stretch;
                padding: 15px;
            }

            .upload-section {
                flex-direction: column;
                width: 100%;
            }

            .upload-btn, .create-folder-btn {
                width: 100%;
                justify-content: center;
            }

            .file-grid {
                grid-template-columns: 1fr;
                gap: 15px;
                padding: 5px;
            }
        }

        @media (max-width: 480px) {
            .header h1 {
                font-size: 1.5em;
            }

            .breadcrumb {
                font-size: 0.9em;
            }

            .file-item {
                padding: 15px;
            }
        }

        /* Light theme variables */
        :root {
            --primary-color: #2563eb;
            --secondary-color: #3b82f6;
            --accent-color: #60a5fa;
            --background-color: #f3f4f6;
            --text-primary: #1f2937;
            --text-secondary: #4b5563;
            --border-color: #e5e7eb;
        }

        body {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--background-color);
            min-height: 100vh;
            padding: 20px;
            color: var(--text-primary);
            transition: all 0.3s ease;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.05);
            overflow: hidden;
            backdrop-filter: blur(10px);
            border: 1px solid var(--border-color);
        }

        .header {
            background: var(--primary-color);
            color: white;
            padding: 40px;
            text-align: left;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, var(--secondary-color), var(--accent-color));
            opacity: 0.3;
            z-index: 0;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }

        .toolbar {
            padding: 20px 30px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.1em;
        }

        .breadcrumb-item {
            color: #667eea;
            text-decoration: none;
            padding: 5px 10px;
            border-radius: 5px;
            transition: background 0.3s;
        }

        .breadcrumb-item:hover {
            background: #f0f0f0;
        }

        .breadcrumb-item.current {
            background: #667eea;
            color: white;
        }

        .upload-section {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .upload-btn, .create-folder-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 12px;
            cursor: pointer;
            font-size: 1em;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 6px rgba(37, 99, 235, 0.1);
        }

        .upload-btn i, .create-folder-btn i {
            font-size: 1.2em;
        }

        .upload-btn:hover, .create-folder-btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }

        .content {
            padding: 30px;
        }

        .file-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 25px;
            margin-top: 30px;
            padding: 10px;
        }

        .file-item {
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 16px;
            padding: 20px;
            text-align: left;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .file-item::before {
            content: '';
            position: absolute;
            inset: 0;
            border-radius: 16px;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: -1;
        }

        .file-item:hover {
            border-color: var(--primary-color);
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(37, 99, 235, 0.1);
        }

        .file-item:hover::before {
            opacity: 0.05;
        }

        .file-item:active {
            transform: translateY(-2px);
        }

        /* Loading animation */
        @keyframes shimmer {
            0% {
                background-position: -468px 0;
            }
            100% {
                background-position: 468px 0;
            }
        }

        .loading-shimmer {
            animation: shimmer 1s linear infinite;
            background: linear-gradient(to right, #f6f7f8 8%, #edeef1 18%, #f6f7f8 33%);
            background-size: 800px 104px;
        }

        /* Upload progress bar */
        .upload-progress {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: var(--border-color);
            border-radius: 0 0 16px 16px;
            overflow: hidden;
        }

        .upload-progress-bar {
            height: 100%;
            background: var(--primary-color);
            transition: width 0.3s ease;
        }

        /* Drag and drop zone */
        .drag-zone {
            border: 2px dashed var(--border-color);
            border-radius: 16px;
            padding: 40px;
            text-align: center;
            color: var(--text-secondary);
            transition: all 0.3s ease;
            margin: 20px;
        }

        .drag-zone.drag-over {
            border-color: var(--primary-color);
            background: rgba(37, 99, 235, 0.05);
        }

        /* Toast notifications */
        .toast {
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 15px 25px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 10px;
            transform: translateY(100px);
            opacity: 0;
            transition: all 0.3s ease;
        }

        .toast.show {
            transform: translateY(0);
            opacity: 1;
        }

        .toast.success {
            border-left: 4px solid #10B981;
        }

        .toast.error {
            border-left: 4px solid #EF4444;
        }

        .file-icon {
            width: 60px;
            height: 60px;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5em;
            border-radius: 10px;
        }

        .file-thumbnail {
            width: 100%;
            height: 120px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 15px;
        }

        .folder-icon {
            background: linear-gradient(135deg, #ffd89b 0%, #19547b 100%);
            color: white;
        }

        .file-icon.image {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        }

        .file-icon.text {
            background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
        }

        .file-icon.json {
            background: linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%);
        }

        .file-icon.other {
            background: linear-gradient(135deg, #c2e9fb 0%, #a1c4fd 100%);
        }

        .file-name {
            font-weight: 600;
            margin-bottom: 8px;
            word-break: break-word;
            color: #333;
        }

        .file-info {
            font-size: 0.9em;
            color: var(--text-secondary);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-top: 1px solid var(--border-color);
        }

        .file-actions {
            display: flex;
            gap: 10px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .file-item:hover .file-actions {
            opacity: 1;
        }

        .action-btn {
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 5px;
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .action-btn:hover {
            background: var(--background-color);
            color: var(--primary-color);
        }

        .loading {
            text-align: center;
            padding: 50px;
            font-size: 1.2em;
            color: #666;
        }

        .error {
            background: #ffe6e6;
            color: #d63031;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #d63031;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.8);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            position: relative;
            margin: 5% auto;
            padding: 0;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 25px 50px rgba(0,0,0,0.3);
        }

        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 1.3em;
            font-weight: 600;
        }

        .close {
            color: white;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: opacity 0.3s;
        }

        .close:hover {
            opacity: 0.7;
        }

        .modal-body {
            padding: 30px;
            max-height: 60vh;
            overflow: auto;
        }

        .preview-content {
            text-align: center;
        }

        .preview-image {
            max-width: 100%;
            max-height: 500px;
            border-radius: 10px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .preview-video {
            max-width: 100%;
            max-height: 500px;
            border-radius: 10px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .preview-text {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: left;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow: auto;
            border: 1px solid #e9ecef;
        }

        .download-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            margin-top: 20px;
            transition: all 0.3s;
        }

        .download-btn:hover {
            background: #218838;
            transform: translateY(-2px);
        }

        .file-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }

        @media (max-width: 768px) {
            .file-grid {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
                gap: 15px;
            }

            .modal-content {
                width: 95%;
                margin: 10% auto;
            }

            .toolbar {
                flex-direction: column;
                align-items: stretch;
            }
        }
    </style>
    <script>
        // Theme handling
        function toggleTheme() {
            const root = document.documentElement;
            const currentTheme = root.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            root.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
        }

        // Initialize theme
        function initTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);
        }

        // Toast notifications
        function showToast(message, type = 'success') {
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.innerHTML = `
                <i class="${type === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-circle'}"></i>
                <span>${message}</span>
            `;
            document.body.appendChild(toast);
            setTimeout(() => toast.classList.add('show'), 100);
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => toast.remove(), 300);
            }, 3000);
        }

        // Drag and drop handling
        function initDragAndDrop() {
            const dragZone = document.querySelector('.drag-zone');
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                dragZone.addEventListener(eventName, preventDefaults, false);
                document.body.addEventListener(eventName, preventDefaults, false);
            });

            ['dragenter', 'dragover'].forEach(eventName => {
                dragZone.addEventListener(eventName, highlight, false);
            });

            ['dragleave', 'drop'].forEach(eventName => {
                dragZone.addEventListener(eventName, unhighlight, false);
            });

            dragZone.addEventListener('drop', handleDrop, false);
        }

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        function highlight(e) {
            const dragZone = document.querySelector('.drag-zone');
            dragZone.classList.add('drag-over');
        }

        function unhighlight(e) {
            const dragZone = document.querySelector('.drag-zone');
            dragZone.classList.remove('drag-over');
        }

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            handleFiles(files);
        }

        function handleFiles(files) {
            [...files].forEach(uploadFile);
        }

        // File upload with progress
        function uploadFile(file) {
            const fileItem = createFileItemElement(file);
            document.querySelector('.file-grid').appendChild(fileItem);

            const formData = new FormData();
            formData.append('file', file);
            // Send original filename separately to avoid encoding issues
            formData.append('originalFileName', file.name);

            const xhr = new XMLHttpRequest();
            xhr.open('POST', '/api/v1.0/files/upload', true);

            xhr.upload.addEventListener('progress', (e) => {
                if (e.lengthComputable) {
                    const percentComplete = (e.loaded / e.total) * 100;
                    updateProgress(fileItem, percentComplete);
                }
            });

            xhr.addEventListener('load', () => {
                if (xhr.status === 200) {
                    showToast(`${file.name} uploaded successfully!`, 'success');
                    completeUpload(fileItem);
                } else {
                    showToast(`Failed to upload ${file.name}`, 'error');
                    failUpload(fileItem);
                }
            });

            xhr.send(formData);
        }

        function createFileItemElement(file) {
            const div = document.createElement('div');
            div.className = 'file-item loading-shimmer';
            div.innerHTML = `
                <div class="file-icon ${getFileIconClass(file.type)}"></div>
                <div class="file-name">${file.name}</div>
                <div class="file-info">
                    <span>${formatFileSize(file.size)}</span>
                </div>
                <div class="upload-progress">
                    <div class="upload-progress-bar" style="width: 0%"></div>
                </div>
            `;
            return div;
        }

        function updateProgress(fileItem, percent) {
            const progressBar = fileItem.querySelector('.upload-progress-bar');
            progressBar.style.width = `${percent}%`;
        }

        function completeUpload(fileItem) {
            fileItem.classList.remove('loading-shimmer');
            setTimeout(() => {
                fileItem.querySelector('.upload-progress').remove();
            }, 300);
        }

        function failUpload(fileItem) {
            fileItem.classList.remove('loading-shimmer');
            fileItem.classList.add('error');
        }

        // Utility functions
        function getFileIconClass(mimeType) {
            if (mimeType.startsWith('image/')) return 'image';
            if (mimeType.startsWith('text/')) return 'text';
            if (mimeType.includes('json')) return 'json';
            if (mimeType.includes('pdf')) return 'pdf';
            if (mimeType.includes('video/')) return 'video';
            if (mimeType.includes('audio/')) return 'audio';
            if (mimeType.includes('zip') || mimeType.includes('rar')) return 'archive';
            return 'other';
        }

        function getFileIcon(mimeType) {
            if (mimeType.startsWith('image/')) return 'fa-image';
            if (mimeType.startsWith('text/')) return 'fa-file-alt';
            if (mimeType.includes('json')) return 'fa-file-code';
            if (mimeType.includes('pdf')) return 'fa-file-pdf';
            if (mimeType.includes('video/')) return 'fa-file-video';
            if (mimeType.includes('audio/')) return 'fa-file-audio';
            if (mimeType.includes('zip') || mimeType.includes('rar')) return 'fa-file-archive';
            return 'fa-file';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            initTheme();
            initDragAndDrop();
        });
    </script>
</head>
<body>
    <button class="theme-toggle" onclick="toggleTheme()">
        <i class="fas fa-moon"></i>
    </button>
    <div class="container">
        <div class="header">
            <h1>📁 Telegram File Storage</h1>
            <p>Manage your files with style and preview them instantly</p>
        </div>

        <div class="toolbar">
            <div class="breadcrumb" id="breadcrumb">
                <a href="#" class="breadcrumb-item current" onclick="loadFolder(null)">🏠 Home</a>
            </div>

            <div class="upload-section">
                <input type="file" id="fileInput" style="display: none;" multiple>
                <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                    <i class="fas fa-upload"></i>
                    Upload Files
                </button>
                <button class="create-folder-btn" onclick="createFolder()">
                    <i class="fas fa-folder-plus"></i>
                    New Folder
                </button>
            </div>
        </div>

        <div class="content">
            <div class="drag-zone">
                <i class="fas fa-cloud-upload-alt fa-3x"></i>
                <p>Drag and drop files here</p>
                <p>or</p>
                <label class="upload-btn">
                    <i class="fas fa-file-upload"></i>
                    Choose Files
                    <input type="file" multiple style="display: none" onchange="handleFiles(this.files)">
                </label>
            </div>
            <div id="loading" class="loading">Loading files...</div>
            <div id="error" class="error" style="display: none;"></div>
            <div id="fileGrid" class="file-grid"></div>
        </div>
    </div>

    <!-- Preview Modal -->
    <div id="previewModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <span class="modal-title" id="modalTitle">File Preview</span>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div id="previewContent" class="preview-content"></div>
                <div class="file-actions">
                    <button class="download-btn" id="downloadBtn">📥 Download</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000/api/v1.0';
        let currentFolder = null;
        let currentFiles = [];

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            loadFolder(null);
            setupFileUpload();
        });

        // Load folder content
        async function loadFolder(folderId) {
            currentFolder = folderId;
            showLoading(true);
            hideError();

            try {
                const url = folderId ? `${API_BASE}/browse/${folderId}` : `${API_BASE}/browse`;
                const response = await fetch(url);
                const data = await response.json();

                if (data.code === 200) {
                    currentFiles = [...data.data.folders, ...data.data.files];
                    renderFiles(data.data);
                    updateBreadcrumb(folderId);
                } else {
                    showError('Failed to load folder: ' + data.message);
                }
            } catch (error) {
                showError('Network error: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        // Render files and folders
        function renderFiles(data) {
            const grid = document.getElementById('fileGrid');
            grid.innerHTML = '';

            // Render folders
            data.folders.forEach(folder => {
                const item = createFileItem(folder, 'folder');
                grid.appendChild(item);
            });

            // Render files
            data.files.forEach(file => {
                const item = createFileItem(file, 'file');
                grid.appendChild(item);
            });

            if (data.folders.length === 0 && data.files.length === 0) {
                grid.innerHTML = '<div style="grid-column: 1/-1; text-align: center; padding: 50px; color: #666;">📂 This folder is empty</div>';
            }
        }

        // Create file/folder item
        function createFileItem(item, type) {
            const div = document.createElement('div');
            div.className = 'file-item';

            if (type === 'folder') {
                div.onclick = () => loadFolder(item.id);
                div.innerHTML = `
                    <div class="file-icon folder-icon">📁</div>
                    <div class="file-name">${item.name}</div>
                    <div class="file-info">
                        <span>Folder</span>
                        <span>${new Date(item.createdAt).toLocaleDateString()}</span>
                    </div>
                `;
            } else {
                div.onclick = () => previewFile(item);

                const fileType = getFileType(item.mimeType);
                const isImage = fileType === 'image';

                div.innerHTML = `
                    ${isImage ?
                        `<img class="file-thumbnail" src="${API_BASE}/files/preview/${item.id}" alt="${item.name}" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                         <div class="file-icon ${fileType}" style="display: none;">${getFileIcon(fileType)}</div>` :
                        `<div class="file-icon ${fileType}">${getFileIcon(fileType)}</div>`
                    }
                    <div class="file-name">${item.name}</div>
                    <div class="file-info">
                        <span>${formatFileSize(item.size)}</span>
                        <span>${new Date(item.uploadDate).toLocaleDateString()}</span>
                    </div>
                `;
            }

            return div;
        }

        // Get file type from MIME type
        function getFileType(mimeType) {
            if (mimeType.startsWith('image/')) return 'image';
            if (mimeType.startsWith('video/')) return 'video';
            if (mimeType.startsWith('audio/')) return 'audio';
            if (mimeType === 'application/pdf') return 'pdf';
            if (mimeType.startsWith('text/') || mimeType.includes('json') ||
                mimeType.includes('javascript') || mimeType.includes('css') ||
                mimeType.includes('xml') || mimeType.includes('yaml')) return 'text';
            if (mimeType.includes('document') || mimeType.includes('word') ||
                mimeType.includes('spreadsheet') || mimeType.includes('excel') ||
                mimeType.includes('presentation') || mimeType.includes('powerpoint') ||
                mimeType.includes('officedocument')) return 'office';
            if (mimeType.includes('zip') || mimeType.includes('rar') ||
                mimeType.includes('tar') || mimeType.includes('gzip') ||
                mimeType.includes('7z') || mimeType.includes('archive')) return 'archive';
            return 'other';
        }

        // Get file icon
        function getFileIcon(type) {
            const icons = {
                image: '🖼️',
                video: '🎥',
                audio: '🎵',
                pdf: '📄',
                text: '📄',
                office: '📝',
                archive: '📦',
                other: '📎'
            };
            return icons[type] || '📎';
        }

        // Format file size
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }

        // Preview file
        async function previewFile(file) {
            document.getElementById('modalTitle').textContent = file.name;
            const previewContent = document.getElementById('previewContent');
            const downloadBtn = document.getElementById('downloadBtn');

            downloadBtn.onclick = () => downloadFile(file.id, file.name);

            previewContent.innerHTML = '<div style="padding: 20px;">Loading preview...</div>';
            document.getElementById('previewModal').style.display = 'block';

            try {
                const fileType = getFileType(file.mimeType);

                if (fileType === 'image') {
                    previewContent.innerHTML = `
                        <img class="preview-image" src="${API_BASE}/files/preview/${file.id}" alt="${file.name}">
                    `;
                } else if (fileType === 'video') {
                    previewContent.innerHTML = `
                        <video class="preview-video" controls style="max-width: 100%; max-height: 60vh;">
                            <source src="${API_BASE}/files/preview/${file.id}" type="${file.mimeType}">
                            Your browser does not support the video tag.
                        </video>
                    `;
                } else if (fileType === 'audio') {
                    previewContent.innerHTML = `
                        <div style="text-align: center; padding: 40px;">
                            <div style="font-size: 4em; margin-bottom: 20px; color: #9C27B0;">🎵</div>
                            <h3>${file.name}</h3>
                            <audio controls style="width: 100%; max-width: 500px; margin-top: 20px;">
                                <source src="${API_BASE}/files/preview/${file.id}" type="${file.mimeType}">
                                Your browser does not support the audio tag.
                            </audio>
                            <p style="margin-top: 20px;">Size: ${formatFileSize(file.size)}</p>
                        </div>
                    `;
                } else if (fileType === 'pdf') {
                    previewContent.innerHTML = `
                        <iframe src="${API_BASE}/files/preview/${file.id}"
                                style="width: 100%; height: 70vh; border: none;"
                                title="${file.name}">
                        </iframe>
                    `;
                } else if (fileType === 'text') {
                    previewContent.innerHTML = `
                        <iframe src="${API_BASE}/files/preview/${file.id}"
                                style="width: 100%; height: 60vh; border: 1px solid #e0e0e0; border-radius: 8px; background: #f8f9fa;"
                                title="${file.name}">
                        </iframe>
                    `;
                } else if (fileType === 'office') {
                    previewContent.innerHTML = `
                        <div style="text-align: center; padding: 40px;">
                            <div style="font-size: 4em; margin-bottom: 20px; color: #2196F3;">📄</div>
                            <h3>${file.name}</h3>
                            <p>Size: ${formatFileSize(file.size)}</p>
                            <p>Type: ${file.mimeType}</p>
                            <div style="margin-top: 30px;">
                                <button onclick="window.open('https://docs.google.com/viewer?url=' + encodeURIComponent('${API_BASE}/files/preview/${file.id}') + '&embedded=true', '_blank')"
                                        style="margin-right: 10px; padding: 10px 20px; background: #2196F3; color: white; border: none; border-radius: 4px; cursor: pointer;">
                                    Open with Google Viewer
                                </button>
                                <button onclick="downloadFile('${file.id}', '${file.name}')"
                                        style="padding: 10px 20px; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer;">
                                    Download to View
                                </button>
                            </div>
                        </div>
                    `;
                } else if (fileType === 'archive') {
                    previewContent.innerHTML = `
                        <div style="text-align: center; padding: 40px;">
                            <div style="font-size: 4em; margin-bottom: 20px; color: #FF9800;">📦</div>
                            <h3>${file.name}</h3>
                            <p>Size: ${formatFileSize(file.size)}</p>
                            <p>Type: Archive File (${file.mimeType})</p>
                            <p style="margin-top: 20px; color: #666; font-style: italic;">Download to extract and view contents</p>
                        </div>
                    `;
                } else {
                    previewContent.innerHTML = `
                        <div style="text-align: center; padding: 40px;">
                            <div style="font-size: 4em; margin-bottom: 20px; color: #757575;">${getFileIcon(fileType)}</div>
                            <h3>${file.name}</h3>
                            <p>Size: ${formatFileSize(file.size)}</p>
                            <p>Type: ${file.mimeType}</p>
                            <p style="margin-top: 20px; color: #666; font-style: italic;">This file type cannot be previewed in browser</p>
                            <button onclick="downloadFile('${file.id}', '${file.name}')"
                                    style="margin-top: 20px; padding: 10px 20px; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer;">
                                Download to View
                            </button>
                        </div>
                    `;
                }
            } catch (error) {
                previewContent.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #d63031;">
                        <h3>Preview Error</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        // Download file
        function downloadFile(fileId, fileName) {
            const link = document.createElement('a');
            link.href = `${API_BASE}/files/download/${fileId}`;
            link.download = fileName;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // Close modal
        function closeModal() {
            document.getElementById('previewModal').style.display = 'none';
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('previewModal');
            if (event.target === modal) {
                closeModal();
            }
        }

        // Setup file upload
        function setupFileUpload() {
            const fileInput = document.getElementById('fileInput');
            fileInput.addEventListener('change', handleFileUpload);
        }

        // Handle file upload
        async function handleFileUpload(event) {
            const files = event.target.files;
            if (files.length === 0) return;

            for (let file of files) {
                await uploadFile(file);
            }

            // Reload current folder
            loadFolder(currentFolder);

            // Clear input
            event.target.value = '';
        }

        // Upload single file
        async function uploadFile(file) {
            const formData = new FormData();
            formData.append('file', file);
            // Send original filename separately to avoid encoding issues
            formData.append('originalFileName', file.name);
            if (currentFolder) {
                formData.append('parentId', currentFolder);
            }

            try {
                const response = await fetch(`${API_BASE}/files/upload`, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                if (result.code !== 200) {
                    throw new Error(result.message);
                }
            } catch (error) {
                showError(`Failed to upload ${file.name}: ${error.message}`);
            }
        }

        // Create folder
        async function createFolder() {
            const name = prompt('Enter folder name:');
            if (!name) return;

            try {
                const response = await fetch(`${API_BASE}/folders/create`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        folderName: name,
                        parentId: currentFolder
                    })
                });

                const result = await response.json();
                if (result.code === 200) {
                    loadFolder(currentFolder);
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                showError('Failed to create folder: ' + error.message);
            }
        }

        // Update breadcrumb
        function updateBreadcrumb(folderId) {
            // Simple breadcrumb - can be enhanced to show full path
            const breadcrumb = document.getElementById('breadcrumb');
            breadcrumb.innerHTML = `
                <a href="#" class="breadcrumb-item ${!folderId ? 'current' : ''}" onclick="loadFolder(null)">🏠 Home</a>
                ${folderId ? '<span> / </span><span class="breadcrumb-item current">Current Folder</span>' : ''}
            `;
        }

        // Utility functions
        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
            document.getElementById('fileGrid').style.display = show ? 'none' : 'grid';
        }

        function showError(message) {
            const errorDiv = document.getElementById('error');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }

        function hideError() {
            document.getElementById('error').style.display = 'none';
        }
    </script>
</body>
</html>
