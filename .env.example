# VisiBox Backend Configuration
# Copy this file to .env and fill in your actual values

# Application Configuration
NODE_ENV=development
PORT=3001
SECRET_KEY=your_jwt_secret_key_here
LOG_LEVEL=info
SERVICE_NAME=VisiBox-BACKEND

# Frontend Configuration
REACT_APP_API_URL=http://localhost:3001
CURRENT_URL=http://localhost:3001

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/visiobox
MONGO_HOST=localhost
MONGO_PORT=27017
MONGO_DATABASE=visiobox
MONGO_USER=
MONGO_PASSWORD=

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DATABASE=0
REDIS_PASSWORD=

# Telegram Configuration
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_CHAT_ID=your_telegram_chat_id_here
TELEGRAM_ADMIN_CHAT_ID=your_telegram_chat_id_here

# OAuth Configuration
OAUTH_ENABLED=false
OAUTH_GOOGLE_ENABLED=false
OAUTH_GOOGLE_CLIENT_ID=your_google_client_id
OAUTH_GOOGLE_CLIENT_SECRET=your_google_client_secret
OAUTH_GOOGLE_CALLBACK_URL=http://localhost:3001/auth/google/callback
OAUTH_TELEGRAM_ENABLED=false
OAUTH_TELEGRAM_BOT_TOKEN=your_telegram_bot_token_for_login
OAUTH_TELEGRAM_CALLBACK_URL=http://localhost:3001/auth/telegram/callback

# Session Configuration
SESSION_SECRET=your_session_secret_key
SESSION_MAX_AGE=********

# Email Configuration
# You can add as many email accounts as needed by incrementing the number
# Only add the ones you actually want to use
EMAIL_SERVICE_1=gmail
EMAIL_USER_1=<EMAIL>
EMAIL_PASS_1=your_app_password_1

# EMAIL_SERVICE_2=gmail
# EMAIL_USER_2=<EMAIL>
# EMAIL_PASS_2=your_app_password_2

# EMAIL_SERVICE_3=gmail
# EMAIL_USER_3=<EMAIL>
# EMAIL_PASS_3=your_app_password_3

# Add more email accounts as needed by continuing the pattern:
# EMAIL_SERVICE_4=gmail
# EMAIL_USER_4=<EMAIL>
# EMAIL_PASS_4=your_app_password_4

# Email Alert Configuration
# Comma-separated list of email addresses for alerts
EMAIL_ALERTS=<EMAIL>,<EMAIL>
