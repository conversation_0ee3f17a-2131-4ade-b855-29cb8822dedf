#!/bin/bash

# Production Setup Script for VisioBox
# This script helps you set up production environment variables securely

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 VisioBox Production Setup${NC}"
echo "=================================="
echo ""

# Check if .env.production already exists
if [ -f ".env.production" ]; then
    echo -e "${YELLOW}⚠️  .env.production already exists!${NC}"
    read -p "Do you want to overwrite it? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${BLUE}Setup cancelled.${NC}"
        exit 0
    fi
fi

# Function to prompt for input with default value
prompt_input() {
    local prompt="$1"
    local var_name="$2"
    local default_value="$3"
    local is_secret="$4"

    if [ "$is_secret" = "true" ]; then
        echo -n -e "${BLUE}$prompt${NC}"
        if [ -n "$default_value" ]; then
            echo -n " [hidden]: "
        else
            echo -n ": "
        fi
        read -s value
        echo
    else
        echo -n -e "${BLUE}$prompt${NC}"
        if [ -n "$default_value" ]; then
            echo -n " [$default_value]: "
        else
            echo -n ": "
        fi
        read value
    fi

    if [ -z "$value" ] && [ -n "$default_value" ]; then
        value="$default_value"
    fi

    eval "$var_name='$value'"
}

echo -e "${GREEN}📱 Telegram Configuration${NC}"
prompt_input "Telegram Bot Token" TELEGRAM_BOT_TOKEN "" "true"
prompt_input "Telegram Chat ID" TELEGRAM_CHAT_ID "" "false"

echo ""
echo -e "${GREEN}🔐 Security Configuration${NC}"
# Generate random secret key if not provided
SECRET_KEY=$(openssl rand -base64 32 2>/dev/null || date +%s | sha256sum | base64 | head -c 32)
echo -e "${BLUE}JWT Secret Key: ${NC}[Generated automatically]"

echo ""
echo -e "${GREEN}🌐 Application Configuration${NC}"
prompt_input "Application URL" CURRENT_URL "http://localhost:3001" "false"
prompt_input "Frontend API URL" REACT_APP_API_URL "http://localhost:3001" "false"

echo ""
echo -e "${GREEN}📧 Email Configuration (Optional)${NC}"
prompt_input "Email Service" EMAIL_SERVICE_1 "gmail" "false"
prompt_input "Email User" EMAIL_USER_1 "" "false"
prompt_input "Email Password" EMAIL_PASS_1 "" "true"
prompt_input "Alert Email" EMAIL_ALERT_1 "<EMAIL>" "false"

# Create .env.production file
echo ""
echo "📝 Creating .env.production file..."

cat > .env.production << EOF
# Production Environment Variables
# Generated on $(date)

# Database Configuration (Docker containers)
MONGODB_URI=mongodb://mongodb:27017/visiobox
MONGO_PORT=27017
MONGO_DATABASE=visiobox
MONGO_USER=
MONGO_PASSWORD=

# Redis Configuration (Docker containers)
REDIS_PORT=6379
REDIS_DATABASE=0
REDIS_PASSWORD=

# Telegram Configuration
TELEGRAM_BOT_TOKEN=$TELEGRAM_BOT_TOKEN
TELEGRAM_CHAT_ID=$TELEGRAM_CHAT_ID
TELEGRAM_ADMIN_CHAT_ID=$TELEGRAM_CHAT_ID

# Application Configuration
SECRET_KEY=$SECRET_KEY
LOG_LEVEL=info
SERVICE_NAME=VISIOBOX-BACKEND
CURRENT_URL=$CURRENT_URL

# OAuth Configuration
OAUTH_ENABLED=false
OAUTH_GOOGLE_ENABLED=false
OAUTH_GOOGLE_CLIENT_ID=your_google_client_id
OAUTH_GOOGLE_CLIENT_SECRET=your_google_client_secret
OAUTH_GOOGLE_CALLBACK_URL=$CURRENT_URL/auth/google/callback
OAUTH_TELEGRAM_ENABLED=false
OAUTH_TELEGRAM_BOT_TOKEN=$TELEGRAM_BOT_TOKEN
OAUTH_TELEGRAM_CALLBACK_URL=$CURRENT_URL/auth/telegram/callback

# Session Configuration
SESSION_SECRET=$SECRET_KEY
SESSION_MAX_AGE=86400000

# Email Configuration
EMAIL_SERVICE_1=$EMAIL_SERVICE_1
EMAIL_USER_1=$EMAIL_USER_1
EMAIL_PASS_1=$EMAIL_PASS_1
EMAIL_SERVICE_2=gmail
EMAIL_USER_2=
EMAIL_PASS_2=
EMAIL_SERVICE_3=gmail
EMAIL_USER_3=
EMAIL_PASS_3=
EMAIL_SERVICE_4=gmail
EMAIL_USER_4=
EMAIL_PASS_4=
EMAIL_SERVICE_5=gmail
EMAIL_USER_5=
EMAIL_PASS_5=

# Email Alert Configuration
EMAIL_ALERT_1=$EMAIL_ALERT_1

# Frontend Configuration
REACT_APP_API_URL=$REACT_APP_API_URL
EOF

echo -e "${GREEN}✅ Production configuration saved to .env.production${NC}"

echo ""
echo -e "${GREEN}🐳 Next Steps:${NC}"
echo "1. Review and edit .env.production if needed"
echo "2. Start the application: docker-compose up -d"
echo "3. Check logs: docker-compose logs -f"
echo ""
echo -e "${YELLOW}⚠️  Security Note:${NC}"
echo "- .env.production contains sensitive data"
echo "- This file is already in .gitignore"
echo "- Never commit this file to version control"
echo ""
echo -e "${GREEN}🎉 Setup completed!${NC}"
