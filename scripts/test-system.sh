#!/bin/bash

# VisioBox System Test Script
echo "🚀 Testing VisioBox System..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to check if service is running
check_service() {
    local service_name=$1
    local port=$2
    local max_attempts=30
    local attempt=1

    echo -n "Checking $service_name on port $port... "

    while [ $attempt -le $max_attempts ]; do
        if nc -z localhost $port 2>/dev/null; then
            echo -e "${GREEN}✓ Running${NC}"
            return 0
        fi
        sleep 2
        attempt=$((attempt + 1))
    done

    echo -e "${RED}✗ Not responding${NC}"
    return 1
}

# Function to test API endpoint
test_api() {
    local endpoint=$1
    local expected_status=$2
    local description=$3

    echo -n "Testing $description... "

    response=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:3000$endpoint")

    if [ "$response" = "$expected_status" ]; then
        echo -e "${GREEN}✓ OK ($response)${NC}"
        return 0
    else
        echo -e "${RED}✗ Failed ($response, expected $expected_status)${NC}"
        return 1
    fi
}

# Check if Docker Compose is running
echo "📋 Checking Docker Compose services..."
if ! docker-compose ps | grep -q "Up"; then
    echo -e "${YELLOW}⚠ Starting Docker Compose services...${NC}"
    docker-compose up -d
    sleep 10
fi

# Check individual services
echo ""
echo "🔍 Checking service health..."
check_service "MongoDB" 27017
check_service "Redis" 6379
check_service "Backend" 3000
check_service "Frontend" 80

# Test API endpoints
echo ""
echo "🧪 Testing API endpoints..."
test_api "/health" "200" "Health check"
test_api "/api/v1.0/browse" "200" "Browse root folder"
test_api "/api/v1.0/search?q=test" "200" "Search functionality"

# Test frontend
echo ""
echo "🌐 Testing frontend..."
echo -n "Frontend accessibility... "
if curl -s -o /dev/null -w "%{http_code}" "http://localhost" | grep -q "200"; then
    echo -e "${GREEN}✓ Accessible${NC}"
else
    echo -e "${RED}✗ Not accessible${NC}"
fi

# Check logs for errors
echo ""
echo "📝 Checking for errors in logs..."
echo -n "Backend errors... "
if docker-compose logs backend 2>/dev/null | grep -i error | tail -5 | grep -q error; then
    echo -e "${YELLOW}⚠ Found errors (check logs)${NC}"
    docker-compose logs backend | grep -i error | tail -3
else
    echo -e "${GREEN}✓ No recent errors${NC}"
fi

# Summary
echo ""
echo "📊 Test Summary:"
echo "- MongoDB: $(check_service "MongoDB" 27017 >/dev/null && echo -e "${GREEN}✓${NC}" || echo -e "${RED}✗${NC}")"
echo "- Redis: $(check_service "Redis" 6379 >/dev/null && echo -e "${GREEN}✓${NC}" || echo -e "${RED}✗${NC}")"
echo "- Backend: $(check_service "Backend" 3000 >/dev/null && echo -e "${GREEN}✓${NC}" || echo -e "${RED}✗${NC}")"
echo "- Frontend: $(check_service "Frontend" 80 >/dev/null && echo -e "${GREEN}✓${NC}" || echo -e "${RED}✗${NC}")"

echo ""
echo "🎉 System test completed!"
echo "📱 Access the application at: http://localhost"
echo "🔧 API documentation: http://localhost:3000/health"
