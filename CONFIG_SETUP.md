# VisiBox Configuration Setup

## Overview
VisiBox now uses environment variables for all sensitive configuration data. This ensures that sensitive information like API keys, passwords, and tokens are not committed to the repository.

## Quick Setup

1. **Copy the example environment file:**
   ```bash
   cp .env.example .env
   ```

2. **Edit the `.env` file with your actual values:**
   ```bash
   nano .env  # or use your preferred editor
   ```

3. **Fill in the required values** (see sections below for details)

## Required Configuration

### Essential Settings
These must be configured for the application to work:

```env
# Application
SECRET_KEY=your_secret_key_here
SESSION_SECRET=your_session_secret_here

# Telegram
TELEGRAM_BOT_TOKEN=your_bot_token_here
TELEGRAM_CHAT_ID=your_chat_id_here
TELEGRAM_ADMIN_CHAT_ID=your_admin_chat_id_here
```

### Database Settings
```env
# MongoDB
MONGO_DATABASE=visiobox
# Add MONGO_USER and <PERSON><PERSON><PERSON><PERSON>_PASSWORD if using authentication

# Redis
# Default localhost settings should work for development
```

## Dynamic Email Configuration

### Adding Email Accounts
You can add as many email accounts as needed by following this pattern:

```env
EMAIL_SERVICE_1=gmail
EMAIL_USER_1=<EMAIL>
EMAIL_PASS_1=your_app_password_1

EMAIL_SERVICE_2=gmail
EMAIL_USER_2=<EMAIL>
EMAIL_PASS_2=your_app_password_2

# Continue with EMAIL_SERVICE_3, EMAIL_USER_3, EMAIL_PASS_3, etc.
```

**Important Notes:**
- Only add the email accounts you actually want to use
- The system will automatically detect how many email accounts you've configured
- Use Gmail App Passwords, not your regular password
- If you don't need email functionality, you can leave these empty

### Email Alerts
Configure multiple alert recipients:

```env
EMAIL_ALERTS=<EMAIL>,<EMAIL>,<EMAIL>
```

## Security Benefits

### Before (Insecure)
- ❌ Sensitive data hardcoded in config files
- ❌ Tokens and passwords visible in repository
- ❌ Fixed number of email accounts
- ❌ Risk of accidentally committing secrets

### After (Secure)
- ✅ All sensitive data in environment variables
- ✅ No secrets in repository
- ✅ Dynamic number of email accounts
- ✅ `.env` file ignored by git
- ✅ Safe to commit configuration code

## Development vs Production

### Development
```env
NODE_ENV=development
MONGO_DATABASE=visiobox_dev
REACT_APP_API_URL=http://localhost:3001
```

### Production
```env
NODE_ENV=production
MONGO_DATABASE=visiobox_prod
REACT_APP_API_URL=https://your-domain.com
```

## Troubleshooting

### Missing Environment Variables
If you see errors about missing configuration, check that:
1. Your `.env` file exists
2. Required variables are set (not empty)
3. No extra spaces around the `=` sign

### Email Configuration
- Use Gmail App Passwords, not regular passwords
- Make sure 2FA is enabled on Gmail accounts
- Test with one email account first, then add more

### Telegram Configuration
- Get bot token from @BotFather
- Get chat ID by messaging your bot and checking updates
- Admin chat ID can be the same as regular chat ID

## Example .env File
```env
# Copy from .env.example and fill in your values
SECRET_KEY=your_32_character_secret_key_here
TELEGRAM_BOT_TOKEN=*********0:ABCdefGHIjklMNOpqrsTUVwxyz
TELEGRAM_CHAT_ID=*********
EMAIL_USER_1=<EMAIL>
EMAIL_PASS_1=myapppassword
EMAIL_ALERTS=<EMAIL>
```
